/************************************************************
 * ????????????????????????��? 
 * ?????Implement.c
 * ????: ?????
 * ??: ?????????????????
 * ???: YunXiang_TechShare  
 * Q Q: 2228398717
 * ???????????
************************************************************/

/************************* ???? *************************/

#include "Implement.h"
#include "task.h"

/************************* ???? *************************/

/************************ ???????? ************************/

uint16_t time_cnt=0;

static TaskHandle_t AppTaskCreate_Handle = NULL;  // ??????????
static TaskHandle_t LED_Task_Handle = NULL;  // LED??????

/************************ ???????? ************************/

static void AppTaskCreate(void);/* ??????????? */
static void LED_Task(void* pvParameters);/* LED_Task??????? */

/************************************************************ 
 * ????:       show_address(u8 mode)
 * ???:       ??LCD??????????? 
 * ????:       1 ???DHCP?????????
  	           ???? ?????????
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void show_address(u8 mode)
{
	u8 buf[50];
	if(mode==1)
	{
		sprintf((char*)buf,"MAC    :%02X.%02X.%02X.%02X.%02X.%02X",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		printf("%s\r\n", buf);
		sprintf((char*)buf,"DHCP IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		printf("%s\r\n", buf);
		sprintf((char*)buf,"DHCP GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		printf("%s\r\n", buf);
		sprintf((char*)buf,"DHCP MASK:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		printf("%s\r\n", buf);
	}
	else
	{
		sprintf((char*)buf,"MAC      :%02X.%02X.%02X.%02X.%02X.%02X",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//???MAC???
		printf("%s\r\n", buf);
		sprintf((char*)buf,"Static IP:%d.%d.%d.%d",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//??????IP???
		printf("%s\r\n", buf);
		sprintf((char*)buf,"Static GW:%d.%d.%d.%d",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//?????????
		printf("%s\r\n", buf);
		sprintf((char*)buf,"Static MASK:%d.%d.%d.%d",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//?????????????
		printf("%s\r\n", buf);
	}
}

/************************************************************ 
 * ????:       Setup_task(void)
 * ???:       ????????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
void Setup_task(void)
{
  BaseType_t xReturn = pdPASS;/* ?????????????????????????pdPASS */

  printf("????????????????????????-FreeRTOS-???????????!\r\n");
   /* ????AppTaskCreate???? */
  xReturn = xTaskCreate((TaskFunction_t )AppTaskCreate,  /* ?????????? */
                        (const char*    )"AppTaskCreate",/* ???????? */
                        (uint16_t       )512,  /* ???????�� */
                        (void*          )NULL,/* ?????????????? */
                        (UBaseType_t    )1, /* ?????????? */
                        (TaskHandle_t*  )&AppTaskCreate_Handle);/* ??????????? */ 
  /* ??????????? */           
  if(pdPASS == xReturn)
    vTaskStartScheduler();   /* ??????????????? */ 
  
  while(1);   /* ??????????��????? */ 
}

/************************************************************ 
 * ????:       AppTaskCreate(void)
 * ???:       ?????????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

static void AppTaskCreate(void)
{
  BaseType_t xReturn = pdPASS;/* ?????????????????????????pdPASS */
  
  taskENTER_CRITICAL();           //?????????
  
  /* ????LED_Task???? */
  xReturn = xTaskCreate((TaskFunction_t )LED_Task, /* ?????????? */
                        (const char*    )"LED_Task",/* ???????? */
                        (uint16_t       )512,   /* ???????�� */
                        (void*          )NULL,	/* ?????????????? */
                        (UBaseType_t    )2,	    /* ?????????? */
                        (TaskHandle_t*  )&LED_Task_Handle);/* ??????????? */
  if(pdPASS == xReturn)
    printf("????LED_Task??????!\r\n");
  
  vTaskDelete(AppTaskCreate_Handle); //???AppTaskCreate????
  
  taskEXIT_CRITICAL();            //????????
}
/************************************************************ 
 * ????:       LED_Task(void)
 * ???:       LED_Task??????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/
static void LED_Task(void* parameter)
{
    static uint16_t network_info_counter = 0;

    while (1)
    {

			  lwip_periodic_handle();	//LWIP??????????????????

			  // ?10??????????????????? (10000ms / 600ms = ??16????)
			  network_info_counter++;
			  if(network_info_counter >= 16)
			  {
			      network_info_counter = 0;
			      printf("=== Network Status ===\r\n");
			      show_address(lwipdev.dhcpstatus);	//?????????
			      printf("======================\r\n");
			  }

        LED1_ON();
        vTaskDelay(100);   /* ???100??tick */
        printf("LED_Task Running,LED1_ON\r\n");

        LED1_OFF();
        vTaskDelay(500);   /* ???500??tick */
        printf("LED_Task Running,LED1_OFF\r\n");
    }
}

/************************************************************ 
 * ????:       System_Init(void)
 * ???:       ???????
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void System_Init(void)
{
    systick_config();     // ???????
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//????NVIC?��????2:2��??????????2��????????
	
	  USART0_Config();  // ????????
	
	  LED_Init();  			//LED?????
	
	  KEY_Init();  			//?????????

	//  LCD_Init(); 			//LCD?????(??????)

	  my_mem_init(SRAMIN);		//????????????
	  my_mem_init(SRAMCCM);	//?????CCM????

		// POINT_COLOR = RED; 		// LCD??????????????
		printf("LYIT GD32F4\r\n");
		printf("Ethernet lwIP Test\r\n");
		printf("LYIT@GD32F470\r\n");
		printf("2024/03/11\r\n");
		Timer3_Init(9999,239); //100hz?????

		while(lwip_comm_init()) //lwip?????
		{
			printf("LWIP Init Failed!\r\n");
			delay_1ms(1200);
			printf("Retrying...\r\n");
		}
    
		#if LWIP_DHCP   //???DHCP
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//???DHCP??????/??????
			{
				lwip_periodic_handle();	//LWIP??????????????????
			}
			printf("System Init Complete!\r\n");
			printf("LWIP Stack Ready!\r\n");
			if(lwipdev.dhcpstatus == 2)
			{
			    printf("DHCP Success!\r\n");
			}
			else
			{
			    printf("DHCP Failed! Using Static IP!\r\n");
			}
		#else
			printf("System Init Complete!\r\n");
			printf("LWIP Stack Ready!\r\n");
			printf("Using Static IP Configuration!\r\n");
		#endif

}

/************************************************************ 
 * ????:       Implement(void)
 * ???:       ??��???
 * ????:       ??
 * ???:       ??
 * ?????:     ??
 * ????        ?????
 * ????:       ??
************************************************************/

void Implement(void)
{
	Setup_task();
}


/****************************End*****************************/

