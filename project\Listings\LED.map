Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    gd32f4xx_it.o(.text) refers to tasks.o(.text) for xTaskGetSchedulerState
    gd32f4xx_it.o(.text) refers to port.o(.text) for xPortSysTickHandler
    main.o(.text) refers to implement.o(.text) for System_Init
    systick.o(.text) refers to gd32f4xx_misc.o(.text) for systick_clksource_set
    systick.o(.text) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    systick.o(.text) refers to systick.o(.data) for .data
    led.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    led.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    key.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    key.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    key.o(.text) refers to systick.o(.text) for delay_1ms
    key.o(.text) refers to key.o(.data) for .data
    lan8720.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    lan8720.o(.text) refers to gd32f4xx_enet.o(.text) for enet_deinit
    lan8720.o(.text) refers to gd32f4xx_syscfg.o(.text) for syscfg_enet_phy_interface_config
    lan8720.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    lan8720.o(.text) refers to systick.o(.text) for delay_1ms
    lan8720.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_vector_table_set
    lan8720.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    lan8720.o(.text) refers to lwip_comm.o(.text) for lwip_pkt_handle
    lan8720.o(.text) refers to malloc.o(.text) for myfree
    lan8720.o(.text) refers to gd32f4xx_enet.o(.data) for dma_current_rxdesc
    lcd.o(.text) refers to systick.o(.text) for delay_1us
    lcd.o(.text) refers to lcd.o(.bss) for .bss
    lcd.o(.text) refers to dfltui.o(.text) for __aeabi_ui2d
    lcd.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    lcd.o(.text) refers to dfixui.o(.text) for __aeabi_d2uiz
    lcd.o(.text) refers to lcd.o(.data) for .data
    lcd.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    lcd.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_mode_set
    lcd.o(.text) refers to gd32f4xx_exmc.o(.text) for exmc_norsram_init
    lcd.o(.text) refers to lcd.o(.constdata) for .constdata
    usart0.o(.text) refers to gd32f4xx_usart.o(.text) for usart_data_transmit
    usart0.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    usart0.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_af_set
    usart0.o(.text) refers to usart0.o(.data) for .data
    implement.o(.text) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    implement.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    implement.o(.text) refers to lwip_comm.o(.text) for lwip_periodic_handle
    implement.o(.text) refers to gd32f4xx_gpio.o(.text) for gpio_bit_set
    implement.o(.text) refers to tasks.o(.text) for vTaskDelay
    implement.o(.text) refers to port.o(.text) for vPortEnterCritical
    implement.o(.text) refers to systick.o(.text) for systick_config
    implement.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_priority_group_set
    implement.o(.text) refers to usart0.o(.text) for USART0_Config
    implement.o(.text) refers to led.o(.text) for LED_Init
    implement.o(.text) refers to key.o(.text) for KEY_Init
    implement.o(.text) refers to malloc.o(.text) for my_mem_init
    implement.o(.text) refers to timer.o(.text) for Timer3_Init
    implement.o(.text) refers to lwip_comm.o(.bss) for lwipdev
    implement.o(.text) refers to implement.o(.data) for .data
    timer.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_clock_enable
    timer.o(.text) refers to gd32f4xx_timer.o(.text) for timer_deinit
    timer.o(.text) refers to gd32f4xx_misc.o(.text) for nvic_irq_enable
    timer.o(.text) refers to lwip_comm.o(.data) for lwip_localtime
    system_gd32f4xx.o(.text) refers to system_gd32f4xx.o(.data) for .data
    gd32f4xx_adc.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_can.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_can.o(.text) refers to gd32f4xx_dbg.o(.text) for dbg_periph_enable
    gd32f4xx_ctc.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_dac.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_dci.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_enet.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.bss) for .bss
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.data) for .data
    gd32f4xx_enet.o(.text) refers to gd32f4xx_enet.o(.constdata) for .constdata
    gd32f4xx_gpio.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_i2c.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_ipa.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_iref.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_pmu.o(.text) refers to gd32f4xx_pmu.o(.bss) for .bss
    gd32f4xx_sdio.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_spi.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_syscfg.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_timer.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_tli.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_trng.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_usart.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    gd32f4xx_wwdgt.o(.text) refers to gd32f4xx_rcu.o(.text) for rcu_periph_reset_enable
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    startup_gd32f450_470.o(RESET) refers to startup_gd32f450_470.o(.text) for Reset_Handler
    startup_gd32f450_470.o(RESET) refers to gd32f4xx_it.o(.text) for NMI_Handler
    startup_gd32f450_470.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_gd32f450_470.o(RESET) refers to timer.o(.text) for TIMER3_IRQHandler
    startup_gd32f450_470.o(RESET) refers to usart0.o(.text) for USART0_IRQHandler
    startup_gd32f450_470.o(RESET) refers to lan8720.o(.text) for ENET_IRQHandler
    startup_gd32f450_470.o(.text) refers to system_gd32f4xx.o(.text) for SystemInit
    startup_gd32f450_470.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    lwip_comm.o(.text) refers to malloc.o(.text) for myfree
    lwip_comm.o(.text) refers to memp.o(.text) for memp_get_memorysize
    lwip_comm.o(.text) refers to lan8720.o(.text) for ETH_Mem_Malloc
    lwip_comm.o(.text) refers to init.o(.text) for lwip_init
    lwip_comm.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    lwip_comm.o(.text) refers to netif.o(.text) for netif_add
    lwip_comm.o(.text) refers to ethernetif.o(.text) for ethernetif_input
    lwip_comm.o(.text) refers to tcp.o(.text) for tcp_tmr
    lwip_comm.o(.text) refers to etharp.o(.text) for etharp_tmr
    lwip_comm.o(.text) refers to memp.o(.data) for memp_memory
    lwip_comm.o(.text) refers to mem.o(.data) for ram_heap
    lwip_comm.o(.text) refers to lwip_comm.o(.bss) for .bss
    lwip_comm.o(.text) refers to lwip_comm.o(.data) for .data
    etharp.o(.text) refers to pbuf.o(.text) for pbuf_free
    etharp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    etharp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    etharp.o(.text) refers to def.o(.text) for lwip_htons
    etharp.o(.text) refers to etharp.o(.bss) for .bss
    etharp.o(.text) refers to etharp.o(.conststring) for .conststring
    etharp.o(.text) refers to memcmp.o(.text) for memcmp
    etharp.o(.text) refers to etharp.o(.constdata) for .constdata
    etharp.o(.text) refers to etharp.o(.data) for .data
    etharp.o(.text) refers to ip.o(.text) for ip_input
    ethernetif.o(.text) refers to lan8720.o(.text) for ETH_GetCurrentTxBuffer
    ethernetif.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    ethernetif.o(.text) refers to pbuf.o(.text) for pbuf_alloc
    ethernetif.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ethernetif.o(.text) refers to gd32f4xx_enet.o(.text) for enet_mac_address_set
    ethernetif.o(.text) refers to etharp.o(.text) for etharp_output
    ethernetif.o(.text) refers to lwip_comm.o(.bss) for lwipdev
    ethernetif.o(.text) refers to gd32f4xx_enet.o(.data) for txdesc_tab
    icmp.o(.text) refers to pbuf.o(.text) for pbuf_header
    icmp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    icmp.o(.text) refers to inet_chksum.o(.text) for inet_chksum_pbuf
    icmp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    icmp.o(.text) refers to ip.o(.text) for ip_output_if
    icmp.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    icmp.o(.text) refers to ip.o(.data) for current_iphdr_dest
    inet_chksum.o(.text) refers to def.o(.text) for lwip_htons
    inet_chksum.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip.o(.text) refers to def.o(.text) for lwip_ntohs
    ip.o(.text) refers to pbuf.o(.text) for pbuf_free
    ip.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    ip.o(.text) refers to ip_frag.o(.text) for ip_reass
    ip.o(.text) refers to raw.o(.text) for raw_input
    ip.o(.text) refers to udp.o(.text) for udp_input
    ip.o(.text) refers to tcp_in.o(.text) for tcp_input
    ip.o(.text) refers to icmp.o(.text) for icmp_input
    ip.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip.o(.text) refers to netif.o(.data) for netif_list
    ip.o(.text) refers to ip.o(.data) for .data
    ip_addr.o(.text) refers to def.o(.text) for lwip_htonl
    ip_addr.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip_addr.o(.text) refers to ip_addr.o(.bss) for .bss
    ip_frag.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    ip_frag.o(.text) refers to memp.o(.text) for memp_free
    ip_frag.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    ip_frag.o(.text) refers to icmp.o(.text) for icmp_time_exceeded
    ip_frag.o(.text) refers to pbuf.o(.text) for pbuf_clen
    ip_frag.o(.text) refers to def.o(.text) for lwip_ntohs
    ip_frag.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    ip_frag.o(.text) refers to ip_frag.o(.data) for .data
    ip_frag.o(.text) refers to inet_chksum.o(.text) for inet_chksum
    init.o(.text) refers to mem.o(.text) for mem_init
    init.o(.text) refers to memp.o(.text) for memp_init
    init.o(.text) refers to netif.o(.text) for netif_init
    init.o(.text) refers to udp.o(.text) for udp_init
    init.o(.text) refers to tcp.o(.text) for tcp_init
    init.o(.text) refers to timers.o(.text) for sys_timeouts_init
    mem.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    mem.o(.text) refers to memseta.o(.text) for __aeabi_memclr
    mem.o(.text) refers to mem.o(.data) for .data
    memp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    memp.o(.text) refers to memp.o(.data) for .data
    memp.o(.text) refers to memp.o(.bss) for .bss
    memp.o(.text) refers to memp.o(.constdata) for .constdata
    netif.o(.text) refers to tcp.o(.text) for tcp_abort
    netif.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    netif.o(.text) refers to etharp.o(.text) for etharp_cleanup_netif
    netif.o(.text) refers to tcp.o(.data) for tcp_active_pcbs
    netif.o(.text) refers to netif.o(.data) for .data
    pbuf.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    pbuf.o(.text) refers to mem.o(.text) for mem_free
    pbuf.o(.text) refers to memp.o(.text) for memp_free
    pbuf.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    pbuf.o(.text) refers to strlen.o(.text) for strlen
    raw.o(.text) refers to pbuf.o(.text) for pbuf_header
    raw.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    raw.o(.text) refers to ip.o(.text) for ip_route
    raw.o(.text) refers to memp.o(.text) for memp_free
    raw.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    raw.o(.text) refers to raw.o(.data) for .data
    raw.o(.text) refers to ip.o(.data) for current_iphdr_dest
    tcp.o(.text) refers to pbuf.o(.text) for pbuf_free
    tcp.o(.text) refers to memp.o(.text) for memp_free
    tcp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp.o(.text) refers to tcp_out.o(.text) for tcp_zero_window_probe
    tcp.o(.text) refers to tcp.o(.data) for .data
    tcp.o(.text) refers to tcp.o(.constdata) for .constdata
    tcp.o(.text) refers to timers.o(.text) for tcp_timer_needed
    tcp.o(.text) refers to ip.o(.text) for ip_route
    tcp.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    tcp.o(.constdata) refers to tcp.o(.conststring) for .conststring
    tcp.o(.constdata) refers to tcp.o(.data) for tcp_listen_pcbs
    tcp_in.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp_in.o(.text) refers to tcp_out.o(.text) for tcp_rexmit_fast
    tcp_in.o(.text) refers to pbuf.o(.text) for pbuf_clen
    tcp_in.o(.text) refers to def.o(.text) for lwip_ntohs
    tcp_in.o(.text) refers to tcp.o(.text) for tcp_seg_free
    tcp_in.o(.text) refers to tcp_in.o(.data) for .data
    tcp_in.o(.text) refers to tcp.o(.data) for tcp_ticks
    tcp_in.o(.text) refers to tcp_in.o(.bss) for .bss
    tcp_in.o(.text) refers to ip.o(.data) for current_iphdr_src
    tcp_in.o(.text) refers to timers.o(.text) for tcp_timer_needed
    tcp_in.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    tcp_in.o(.text) refers to memp.o(.text) for memp_free
    tcp_out.o(.text) refers to pbuf.o(.text) for pbuf_alloc
    tcp_out.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tcp_out.o(.text) refers to def.o(.text) for lwip_htons
    tcp_out.o(.text) refers to memp.o(.text) for memp_malloc
    tcp_out.o(.text) refers to tcp.o(.text) for tcp_seg_free
    tcp_out.o(.text) refers to tcp_out.o(.conststring) for .conststring
    tcp_out.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    tcp_out.o(.text) refers to ip.o(.text) for ip_output
    tcp_out.o(.text) refers to tcp.o(.data) for tcp_ticks
    tcp_out.o(.text) refers to tcp_in.o(.data) for tcp_input_pcb
    timers.o(.text) refers to memp.o(.text) for memp_malloc
    timers.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    timers.o(.text) refers to tcp.o(.text) for tcp_tmr
    timers.o(.text) refers to ip_frag.o(.text) for ip_reass_tmr
    timers.o(.text) refers to etharp.o(.text) for etharp_tmr
    timers.o(.text) refers to sys_arch.o(.text) for sys_now
    timers.o(.text) refers to timers.o(.data) for .data
    timers.o(.text) refers to tcp.o(.data) for tcp_active_pcbs
    udp.o(.text) refers to pbuf.o(.text) for pbuf_header
    udp.o(.text) refers to ip_addr.o(.text) for ip4_addr_isbroadcast
    udp.o(.text) refers to def.o(.text) for lwip_ntohs
    udp.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    udp.o(.text) refers to icmp.o(.text) for icmp_dest_unreach
    udp.o(.text) refers to ip.o(.text) for ip_output_if
    udp.o(.text) refers to memp.o(.text) for memp_free
    udp.o(.text) refers to memseta.o(.text) for __aeabi_memclr4
    udp.o(.text) refers to ip.o(.data) for current_iphdr_dest
    udp.o(.text) refers to udp.o(.data) for .data
    sys_arch.o(.text) refers to lwip_comm.o(.data) for lwip_localtime
    malloc.o(.text) refers to malloc.o(.constdata) for .constdata
    malloc.o(.text) refers to malloc.o(.data) for .data
    malloc.o(.data) refers to malloc.o(.text) for my_mem_init
    malloc.o(.data) refers to malloc.o(.bss) for mem1base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68000000) for mem2base
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x10000000) for mem3base
    malloc.o(.data) refers to malloc.o(.bss) for mem1mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x68032000) for mem2mapbase
    malloc.o(.data) refers to malloc.o(.ARM.__AT_0x1000F000) for mem3mapbase
    event_groups.o(.text) refers to heap_4.o(.text) for pvPortMalloc
    event_groups.o(.text) refers to list.o(.text) for vListInitialise
    event_groups.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    event_groups.o(.text) refers to tasks.o(.text) for vTaskSuspendAll
    event_groups.o(.text) refers to port.o(.text) for vPortEnterCritical
    queue.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    queue.o(.text) refers to port.o(.text) for vPortEnterCritical
    queue.o(.text) refers to list.o(.text) for vListInitialise
    queue.o(.text) refers to tasks.o(.text) for xTaskRemoveFromEventList
    queue.o(.text) refers to heap_4.o(.text) for pvPortMalloc
    queue.o(.text) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(.text) refers to queue.o(.bss) for .bss
    tasks.o(.text) refers to port.o(.text) for vPortEnterCritical
    tasks.o(.text) refers to list.o(.text) for vListInitialise
    tasks.o(.text) refers to heap_4.o(.text) for pvPortMalloc
    tasks.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    tasks.o(.text) refers to tasks.o(.data) for .data
    tasks.o(.text) refers to tasks.o(.bss) for .bss
    heap_4.o(.text) refers to tasks.o(.text) for vTaskSuspendAll
    heap_4.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    heap_4.o(.text) refers to heap_4.o(.data) for .data
    heap_4.o(.text) refers to heap_4.o(.bss) for .bss
    port.o(.emb_text) refers to tasks.o(.text) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(.text) refers to printf8.o(i.__0printf$8) for __2printf
    port.o(.text) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvEnableVFP
    port.o(.text) refers to tasks.o(.text) for xTaskIncrementTick
    port.o(.text) refers to port.o(.data) for .data
    port.o(.text) refers to system_gd32f4xx.o(.data) for SystemCoreClock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to usart0.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart0.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart0.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to usart0.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart0.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to usart0.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to usart0.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart0.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to usart0.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to usart0.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart0.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to usart0.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to usart0.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart0.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to usart0.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to usart0.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart0.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to usart0.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to usart0.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart0.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to usart0.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to usart0.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart0.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to usart0.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to usart0.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart0.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to usart0.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to usart0.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart0.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to usart0.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0printf) refers to usart0.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart0.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to usart0.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_gd32f450_470.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing gd32f4xx_it.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_it.o(.revsh_text), (4 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing systick.o(.rev16_text), (4 bytes).
    Removing systick.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing lan8720.o(.rev16_text), (4 bytes).
    Removing lan8720.o(.revsh_text), (4 bytes).
    Removing lcd.o(.rev16_text), (4 bytes).
    Removing lcd.o(.revsh_text), (4 bytes).
    Removing lcd.o(.text), (15400 bytes).
    Removing lcd.o(.bss), (14 bytes).
    Removing lcd.o(.constdata), (6080 bytes).
    Removing lcd.o(.data), (4 bytes).
    Removing usart0.o(.rev16_text), (4 bytes).
    Removing usart0.o(.revsh_text), (4 bytes).
    Removing implement.o(.rev16_text), (4 bytes).
    Removing implement.o(.revsh_text), (4 bytes).
    Removing implement.o(.data), (2 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing system_gd32f4xx.o(.rev16_text), (4 bytes).
    Removing system_gd32f4xx.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_adc.o(.text), (1440 bytes).
    Removing gd32f4xx_can.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_can.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_can.o(.text), (1668 bytes).
    Removing gd32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_crc.o(.text), (88 bytes).
    Removing gd32f4xx_ctc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ctc.o(.text), (404 bytes).
    Removing gd32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dac.o(.text), (816 bytes).
    Removing gd32f4xx_dbg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dbg.o(.text), (108 bytes).
    Removing gd32f4xx_dci.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dci.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dci.o(.text), (308 bytes).
    Removing gd32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_dma.o(.text), (1292 bytes).
    Removing gd32f4xx_enet.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_enet.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exmc.o(.text), (1936 bytes).
    Removing gd32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_exti.o(.text), (256 bytes).
    Removing gd32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fmc.o(.text), (1364 bytes).
    Removing gd32f4xx_fwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_fwdgt.o(.text), (136 bytes).
    Removing gd32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_i2c.o(.text), (808 bytes).
    Removing gd32f4xx_ipa.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_ipa.o(.text), (936 bytes).
    Removing gd32f4xx_iref.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_iref.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_iref.o(.text), (124 bytes).
    Removing gd32f4xx_misc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_misc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_pmu.o(.text), (560 bytes).
    Removing gd32f4xx_pmu.o(.bss), (16 bytes).
    Removing gd32f4xx_rcu.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rcu.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_rtc.o(.text), (2180 bytes).
    Removing gd32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_sdio.o(.text), (740 bytes).
    Removing gd32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_spi.o(.text), (904 bytes).
    Removing gd32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_timer.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_timer.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_tli.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_tli.o(.text), (856 bytes).
    Removing gd32f4xx_trng.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_trng.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_trng.o(.text), (132 bytes).
    Removing gd32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.rev16_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.revsh_text), (4 bytes).
    Removing gd32f4xx_wwdgt.o(.text), (140 bytes).
    Removing startup_gd32f450_470.o(HEAP), (1024 bytes).
    Removing lwip_comm.o(.rev16_text), (4 bytes).
    Removing lwip_comm.o(.revsh_text), (4 bytes).
    Removing ethernetif.o(.rev16_text), (4 bytes).
    Removing ethernetif.o(.revsh_text), (4 bytes).
    Removing ip_addr.o(.constdata), (4 bytes).
    Removing ip_addr.o(.constdata), (4 bytes).
    Removing sys_arch.o(.rev16_text), (4 bytes).
    Removing sys_arch.o(.revsh_text), (4 bytes).
    Removing malloc.o(.rev16_text), (4 bytes).
    Removing malloc.o(.revsh_text), (4 bytes).
    Removing croutine.o(.rev16_text), (4 bytes).
    Removing croutine.o(.revsh_text), (4 bytes).
    Removing event_groups.o(.rev16_text), (4 bytes).
    Removing event_groups.o(.revsh_text), (4 bytes).
    Removing event_groups.o(.text), (800 bytes).
    Removing list.o(.rev16_text), (4 bytes).
    Removing list.o(.revsh_text), (4 bytes).
    Removing queue.o(.rev16_text), (4 bytes).
    Removing queue.o(.revsh_text), (4 bytes).
    Removing queue.o(.text), (2068 bytes).
    Removing queue.o(.bss), (80 bytes).
    Removing tasks.o(.rev16_text), (4 bytes).
    Removing tasks.o(.revsh_text), (4 bytes).
    Removing timers_1.o(.rev16_text), (4 bytes).
    Removing timers_1.o(.revsh_text), (4 bytes).
    Removing heap_4.o(.rev16_text), (4 bytes).
    Removing heap_4.o(.revsh_text), (4 bytes).
    Removing port.o(.rev16_text), (4 bytes).
    Removing port.o(.revsh_text), (4 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing dfltui.o(.text), (26 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing depilogue.o(.text), (186 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

146 unused section(s) (total 44250 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\CMSIS\system_gd32f4xx.c               0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\FreeRTOS\port\MemMang\heap_4.c        0x00000000   Number         0  heap_4.o ABSOLUTE
    ..\FreeRTOS\port\RVDS\ARM_CM4F\port.c    0x00000000   Number         0  port.o ABSOLUTE
    ..\FreeRTOS\src\croutine.c               0x00000000   Number         0  croutine.o ABSOLUTE
    ..\FreeRTOS\src\event_groups.c           0x00000000   Number         0  event_groups.o ABSOLUTE
    ..\FreeRTOS\src\list.c                   0x00000000   Number         0  list.o ABSOLUTE
    ..\FreeRTOS\src\queue.c                  0x00000000   Number         0  queue.o ABSOLUTE
    ..\FreeRTOS\src\tasks.c                  0x00000000   Number         0  tasks.o ABSOLUTE
    ..\FreeRTOS\src\timers.c                 0x00000000   Number         0  timers_1.o ABSOLUTE
    ..\HardWare\KEY\KEY.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HardWare\LAN8720\lan8720.c            0x00000000   Number         0  lan8720.o ABSOLUTE
    ..\HardWare\LCD\LCD.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HardWare\LED\LED.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\Implement\Implement.c                 0x00000000   Number         0  implement.o ABSOLUTE
    ..\LWIP\arch\sys_arch.c                  0x00000000   Number         0  sys_arch.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\api_lib.c     0x00000000   Number         0  api_lib.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\api_msg.c     0x00000000   Number         0  api_msg.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\err.c         0x00000000   Number         0  err.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netbuf.c      0x00000000   Number         0  netbuf.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netdb.c       0x00000000   Number         0  netdb.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\netifapi.c    0x00000000   Number         0  netifapi.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\sockets.c     0x00000000   Number         0  sockets.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\api\tcpip.c       0x00000000   Number         0  tcpip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\def.c        0x00000000   Number         0  def.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\dhcp.c       0x00000000   Number         0  dhcp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\dns.c        0x00000000   Number         0  dns.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\init.c       0x00000000   Number         0  init.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\autoip.c 0x00000000   Number         0  autoip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\icmp.c  0x00000000   Number         0  icmp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\igmp.c  0x00000000   Number         0  igmp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\inet.c  0x00000000   Number         0  inet.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\inet_chksum.c 0x00000000   Number         0  inet_chksum.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip.c    0x00000000   Number         0  ip.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip_addr.c 0x00000000   Number         0  ip_addr.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\ipv4\ip_frag.c 0x00000000   Number         0  ip_frag.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\lwip_sys.c   0x00000000   Number         0  lwip_sys.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\mem.c        0x00000000   Number         0  mem.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\memp.c       0x00000000   Number         0  memp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\netif.c      0x00000000   Number         0  netif.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\pbuf.c       0x00000000   Number         0  pbuf.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\raw.c        0x00000000   Number         0  raw.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\stats.c      0x00000000   Number         0  stats.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp.c        0x00000000   Number         0  tcp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp_in.c     0x00000000   Number         0  tcp_in.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\tcp_out.c    0x00000000   Number         0  tcp_out.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\timers.c     0x00000000   Number         0  timers.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\core\udp.c        0x00000000   Number         0  udp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\netif\etharp.c    0x00000000   Number         0  etharp.o ABSOLUTE
    ..\LWIP\lwip-1.4.1\src\netif\ethernetif.c 0x00000000   Number         0  ethernetif.o ABSOLUTE
    ..\LWIP\lwip_app\lwip_comm\lwip_comm.c   0x00000000   Number         0  lwip_comm.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\MALLOC\malloc.c                       0x00000000   Number         0  malloc.o ABSOLUTE
    ..\Protocol\USART0\USART0.c              0x00000000   Number         0  usart0.o ABSOLUTE
    ..\Startup\startup_gd32f450_470.s        0x00000000   Number         0  startup_gd32f450_470.o ABSOLUTE
    ..\System\TIMER\TIMER.c                  0x00000000   Number         0  timer.o ABSOLUTE
    ..\User\gd32f4xx_it.c                    0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\User\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\User\systick.c                        0x00000000   Number         0  systick.o ABSOLUTE
    ..\\CMSIS\\system_gd32f4xx.c             0x00000000   Number         0  system_gd32f4xx.o ABSOLUTE
    ..\\FreeRTOS\\port\\MemMang\\heap_4.c    0x00000000   Number         0  heap_4.o ABSOLUTE
    ..\\FreeRTOS\\port\\RVDS\\ARM_CM4F\\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\FreeRTOS\\src\\croutine.c            0x00000000   Number         0  croutine.o ABSOLUTE
    ..\\FreeRTOS\\src\\event_groups.c        0x00000000   Number         0  event_groups.o ABSOLUTE
    ..\\FreeRTOS\\src\\list.c                0x00000000   Number         0  list.o ABSOLUTE
    ..\\FreeRTOS\\src\\queue.c               0x00000000   Number         0  queue.o ABSOLUTE
    ..\\FreeRTOS\\src\\tasks.c               0x00000000   Number         0  tasks.o ABSOLUTE
    ..\\FreeRTOS\\src\\timers.c              0x00000000   Number         0  timers_1.o ABSOLUTE
    ..\\HardWare\\KEY\\KEY.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HardWare\\LAN8720\\lan8720.c         0x00000000   Number         0  lan8720.o ABSOLUTE
    ..\\HardWare\\LCD\\LCD.c                 0x00000000   Number         0  lcd.o ABSOLUTE
    ..\\HardWare\\LED\\LED.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\Implement\\Implement.c               0x00000000   Number         0  implement.o ABSOLUTE
    ..\\LWIP\\arch\\sys_arch.c               0x00000000   Number         0  sys_arch.o ABSOLUTE
    ..\\LWIP\\lwip-1.4.1\\src\\netif\\ethernetif.c 0x00000000   Number         0  ethernetif.o ABSOLUTE
    ..\\LWIP\\lwip_app\\lwip_comm\\lwip_comm.c 0x00000000   Number         0  lwip_comm.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_adc.c 0x00000000   Number         0  gd32f4xx_adc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_can.c 0x00000000   Number         0  gd32f4xx_can.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_crc.c 0x00000000   Number         0  gd32f4xx_crc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ctc.c 0x00000000   Number         0  gd32f4xx_ctc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dac.c 0x00000000   Number         0  gd32f4xx_dac.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dbg.c 0x00000000   Number         0  gd32f4xx_dbg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dci.c 0x00000000   Number         0  gd32f4xx_dci.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_dma.c 0x00000000   Number         0  gd32f4xx_dma.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_enet.c 0x00000000   Number         0  gd32f4xx_enet.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exmc.c 0x00000000   Number         0  gd32f4xx_exmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_exti.c 0x00000000   Number         0  gd32f4xx_exti.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fmc.c 0x00000000   Number         0  gd32f4xx_fmc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_fwdgt.c 0x00000000   Number         0  gd32f4xx_fwdgt.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_gpio.c 0x00000000   Number         0  gd32f4xx_gpio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_i2c.c 0x00000000   Number         0  gd32f4xx_i2c.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_ipa.c 0x00000000   Number         0  gd32f4xx_ipa.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_iref.c 0x00000000   Number         0  gd32f4xx_iref.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_misc.c 0x00000000   Number         0  gd32f4xx_misc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_pmu.c 0x00000000   Number         0  gd32f4xx_pmu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rcu.c 0x00000000   Number         0  gd32f4xx_rcu.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_rtc.c 0x00000000   Number         0  gd32f4xx_rtc.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_sdio.c 0x00000000   Number         0  gd32f4xx_sdio.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_spi.c 0x00000000   Number         0  gd32f4xx_spi.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_syscfg.c 0x00000000   Number         0  gd32f4xx_syscfg.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_timer.c 0x00000000   Number         0  gd32f4xx_timer.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_tli.c 0x00000000   Number         0  gd32f4xx_tli.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_trng.c 0x00000000   Number         0  gd32f4xx_trng.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_usart.c 0x00000000   Number         0  gd32f4xx_usart.o ABSOLUTE
    ..\\Library\\GD32F4xx_standard_peripheral\\Source\\gd32f4xx_wwdgt.c 0x00000000   Number         0  gd32f4xx_wwdgt.o ABSOLUTE
    ..\\MALLOC\\malloc.c                     0x00000000   Number         0  malloc.o ABSOLUTE
    ..\\Protocol\\USART0\\USART0.c           0x00000000   Number         0  usart0.o ABSOLUTE
    ..\\System\\TIMER\\TIMER.c               0x00000000   Number         0  timer.o ABSOLUTE
    ..\\User\\gd32f4xx_it.c                  0x00000000   Number         0  gd32f4xx_it.o ABSOLUTE
    ..\\User\\main.c                         0x00000000   Number         0  main.o ABSOLUTE
    ..\\User\\systick.c                      0x00000000   Number         0  systick.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_gd32f450_470.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080001bc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x080001c0   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x080001c0   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x080001c0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001c0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x080001c4   Section      182  port.o(.emb_text)
    $v0                                      0x080001c4   Number         0  port.o(.emb_text)
    .text                                    0x0800027a   Section        0  gd32f4xx_it.o(.text)
    .text                                    0x0800029a   Section        0  main.o(.text)
    .text                                    0x080002a8   Section        0  systick.o(.text)
    .text                                    0x08000374   Section        0  led.o(.text)
    .text                                    0x080003b0   Section        0  key.o(.text)
    .text                                    0x080004c0   Section        0  lan8720.o(.text)
    .text                                    0x080007b4   Section        0  usart0.o(.text)
    .text                                    0x080008f0   Section        0  implement.o(.text)
    LED_Task                                 0x080009cb   Thumb Code    90  implement.o(.text)
    AppTaskCreate                            0x08000a25   Thumb Code    54  implement.o(.text)
    .text                                    0x08000d84   Section        0  timer.o(.text)
    .text                                    0x08000e18   Section        0  system_gd32f4xx.o(.text)
    system_clock_240m_25m_hxtal              0x08000e19   Thumb Code   156  system_gd32f4xx.o(.text)
    .text                                    0x08000fd4   Section        0  gd32f4xx_enet.o(.text)
    enet_delay                               0x0800200b   Thumb Code    44  gd32f4xx_enet.o(.text)
    .text                                    0x08002036   Section        0  gd32f4xx_gpio.o(.text)
    .text                                    0x080021cc   Section        0  gd32f4xx_misc.o(.text)
    .text                                    0x080022cc   Section        0  gd32f4xx_rcu.o(.text)
    .text                                    0x0800294c   Section        0  gd32f4xx_syscfg.o(.text)
    .text                                    0x08002a08   Section        0  gd32f4xx_timer.o(.text)
    .text                                    0x0800366c   Section        0  gd32f4xx_usart.o(.text)
    .text                                    0x08003acc   Section       36  startup_gd32f450_470.o(.text)
    $v0                                      0x08003acc   Number         0  startup_gd32f450_470.o(.text)
    .text                                    0x08003af0   Section        0  lwip_comm.o(.text)
    .text                                    0x08003d84   Section        0  etharp.o(.text)
    etharp_free_entry                        0x08003d85   Thumb Code    32  etharp.o(.text)
    etharp_find_entry                        0x08003de7   Thumb Code   332  etharp.o(.text)
    etharp_send_ip                           0x08003f33   Thumb Code    70  etharp.o(.text)
    etharp_update_arp_entry                  0x08003f79   Thumb Code   142  etharp.o(.text)
    etharp_arp_input                         0x0800407b   Thumb Code   570  etharp.o(.text)
    etharp_raw                               0x080042b5   Thumb Code   234  etharp.o(.text)
    etharp_output_to_arp_index               0x080043bf   Thumb Code    84  etharp.o(.text)
    .text                                    0x0800480c   Section        0  ethernetif.o(.text)
    low_level_output                         0x0800480d   Thumb Code    52  ethernetif.o(.text)
    low_level_init                           0x080048e5   Thumb Code   126  ethernetif.o(.text)
    .text                                    0x080049dc   Section        0  icmp.o(.text)
    icmp_send_response                       0x08004b13   Thumb Code   120  icmp.o(.text)
    .text                                    0x08004d0c   Section        0  inet_chksum.o(.text)
    lwip_standard_chksum                     0x08004d0d   Thumb Code    86  inet_chksum.o(.text)
    .text                                    0x08004f58   Section        0  ip.o(.text)
    .text                                    0x0800528c   Section        0  ip_addr.o(.text)
    .text                                    0x080054e0   Section        0  ip_frag.o(.text)
    ip_reass_dequeue_datagram                0x080054e1   Thumb Code    52  ip_frag.o(.text)
    ip_reass_free_complete_datagram          0x08005515   Thumb Code   202  ip_frag.o(.text)
    ip_reass_remove_oldest_datagram          0x08005607   Thumb Code   110  ip_frag.o(.text)
    ip_reass_chain_frag_into_datagram_and_validate 0x08005675   Thumb Code   344  ip_frag.o(.text)
    ip_frag_free_pbuf_custom_ref             0x08005b05   Thumb Code    36  ip_frag.o(.text)
    ipfrag_free_pbuf_custom                  0x08005b29   Thumb Code    40  ip_frag.o(.text)
    .text                                    0x08005d0c   Section        0  def.o(.text)
    .text                                    0x08005d1c   Section        0  init.o(.text)
    .text                                    0x08005d3c   Section        0  mem.o(.text)
    plug_holes                               0x08005d3d   Thumb Code   154  mem.o(.text)
    .text                                    0x0800626c   Section        0  memp.o(.text)
    .text                                    0x080063e8   Section        0  netif.o(.text)
    .text                                    0x0800663c   Section        0  pbuf.o(.text)
    .text                                    0x08007358   Section        0  raw.o(.text)
    .text                                    0x08007550   Section        0  tcp.o(.text)
    tcp_close_shutdown                       0x0800792b   Thumb Code   814  tcp.o(.text)
    tcp_new_port                             0x08007f19   Thumb Code   276  tcp.o(.text)
    tcp_accept_null                          0x080080b5   Thumb Code     6  tcp.o(.text)
    .text                                    0x08008540   Section        0  tcp_in.o(.text)
    tcp_receive                              0x08008541   Thumb Code  1584  tcp_in.o(.text)
    tcp_parseopt                             0x08008b71   Thumb Code   124  tcp_in.o(.text)
    tcp_process                              0x08008bed   Thumb Code  1108  tcp_in.o(.text)
    tcp_listen_input                         0x08009041   Thumb Code   208  tcp_in.o(.text)
    tcp_timewait_input                       0x080095cd   Thumb Code    92  tcp_in.o(.text)
    .text                                    0x0800965c   Section        0  tcp_out.o(.text)
    tcp_output_alloc_header                  0x0800965d   Thumb Code   128  tcp_out.o(.text)
    tcp_create_segment                       0x080096dd   Thumb Code   154  tcp_out.o(.text)
    tcp_pbuf_prealloc                        0x080098d9   Thumb Code   100  tcp_out.o(.text)
    tcp_write_checks                         0x0800993d   Thumb Code   112  tcp_out.o(.text)
    tcp_output_segment                       0x08009f77   Thumb Code   528  tcp_out.o(.text)
    .text                                    0x0800a5bc   Section        0  timers.o(.text)
    tcpip_tcp_timer                          0x0800a625   Thumb Code    38  timers.o(.text)
    ip_reass_timer                           0x0800a66d   Thumb Code    20  timers.o(.text)
    arp_timer                                0x0800a681   Thumb Code    20  timers.o(.text)
    .text                                    0x0800a800   Section        0  udp.o(.text)
    .text                                    0x0800ac60   Section        0  sys_arch.o(.text)
    .text                                    0x0800ac6c   Section        0  malloc.o(.text)
    .text                                    0x0800ae34   Section        0  list.o(.text)
    .text                                    0x0800aebc   Section        0  tasks.o(.text)
    prvAddNewTaskToReadyList                 0x0800aebd   Thumb Code   190  tasks.o(.text)
    prvAddCurrentTaskToDelayedList           0x0800b1db   Thumb Code   102  tasks.o(.text)
    prvTaskIsTaskSuspended                   0x0800b583   Thumb Code    48  tasks.o(.text)
    prvIdleTask                              0x0800b6a5   Thumb Code    30  tasks.o(.text)
    prvInitialiseNewTask                     0x0800bd0d   Thumb Code   136  tasks.o(.text)
    prvResetNextTaskUnblockTime              0x0800bd95   Thumb Code    26  tasks.o(.text)
    prvDeleteTCB                             0x0800bdaf   Thumb Code    20  tasks.o(.text)
    prvCheckTasksWaitingTermination          0x0800bdc3   Thumb Code    76  tasks.o(.text)
    .text                                    0x0800be48   Section        0  heap_4.o(.text)
    prvInsertBlockIntoFreeList               0x0800be49   Thumb Code    72  heap_4.o(.text)
    prvHeapInit                              0x0800bfd9   Thumb Code    66  heap_4.o(.text)
    .text                                    0x0800c058   Section        0  port.o(.text)
    prvTaskExitError                         0x0800c059   Thumb Code    36  port.o(.text)
    .text                                    0x0800c2b4   Section        0  memcpya.o(.text)
    .text                                    0x0800c2d8   Section        0  memseta.o(.text)
    .text                                    0x0800c2fc   Section        0  strlen.o(.text)
    .text                                    0x0800c30a   Section        0  memcmp.o(.text)
    .text                                    0x0800c324   Section        0  uldiv.o(.text)
    .text                                    0x0800c386   Section        0  llushr.o(.text)
    .text                                    0x0800c3a8   Section       36  init.o(.text)
    .text                                    0x0800c3cc   Section        0  llshl.o(.text)
    .text                                    0x0800c3ea   Section        0  __dczerorl2.o(.text)
    i.__0printf$8                            0x0800c440   Section        0  printf8.o(i.__0printf$8)
    i.__0sprintf$8                           0x0800c460   Section        0  printf8.o(i.__0sprintf$8)
    i.__scatterload_copy                     0x0800c488   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800c496   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800c498   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x0800c4a8   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x0800c4a9   Thumb Code   984  printf8.o(i._printf_core)
    i._printf_post_padding                   0x0800c8ac   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x0800c8ad   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x0800c8d0   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x0800c8d1   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i._sputc                                 0x0800c8fe   Section        0  printf8.o(i._sputc)
    _sputc                                   0x0800c8ff   Thumb Code    10  printf8.o(i._sputc)
    .constdata                               0x0800c908   Section      116  gd32f4xx_enet.o(.constdata)
    enet_reg_tab                             0x0800c908   Data         116  gd32f4xx_enet.o(.constdata)
    .constdata                               0x0800c97c   Section       12  etharp.o(.constdata)
    .constdata                               0x0800c988   Section       40  memp.o(.constdata)
    memp_sizes                               0x0800c988   Data          20  memp.o(.constdata)
    memp_num                                 0x0800c99c   Data          20  memp.o(.constdata)
    .constdata                               0x0800c9b0   Section       84  tcp.o(.constdata)
    .constdata                               0x0800ca04   Section       36  malloc.o(.constdata)
    .conststring                             0x0800ca28   Section       68  etharp.o(.conststring)
    .conststring                             0x0800ca6c   Section      117  tcp.o(.conststring)
    .conststring                             0x0800cae4   Section       85  tcp_out.o(.conststring)
    .ARM.__AT_0x10000000                     0x10000000   Section    61440  malloc.o(.ARM.__AT_0x10000000)
    .ARM.__AT_0x1000F000                     0x1000f000   Section     3840  malloc.o(.ARM.__AT_0x1000F000)
    .data                                    0x20000000   Section        8  systick.o(.data)
    count_1us                                0x20000000   Data           4  systick.o(.data)
    count_1ms                                0x20000004   Data           4  systick.o(.data)
    .data                                    0x20000008   Section        1  key.o(.data)
    key_up                                   0x20000008   Data           1  key.o(.data)
    .data                                    0x2000000a   Section        2  usart0.o(.data)
    .data                                    0x2000000c   Section        4  usart0.o(.data)
    .data                                    0x20000010   Section       12  implement.o(.data)
    network_info_counter                     0x20000010   Data           2  implement.o(.data)
    AppTaskCreate_Handle                     0x20000014   Data           4  implement.o(.data)
    LED_Task_Handle                          0x20000018   Data           4  implement.o(.data)
    .data                                    0x2000001c   Section        4  system_gd32f4xx.o(.data)
    .data                                    0x20000020   Section       28  gd32f4xx_enet.o(.data)
    enet_unknow_err                          0x20000028   Data           4  gd32f4xx_enet.o(.data)
    .data                                    0x2000003c   Section       12  lwip_comm.o(.data)
    .data                                    0x20000048   Section        1  etharp.o(.data)
    etharp_cached_entry                      0x20000048   Data           1  etharp.o(.data)
    .data                                    0x2000004c   Section       20  ip.o(.data)
    ip_id                                    0x2000004c   Data           2  ip.o(.data)
    .data                                    0x20000060   Section        8  ip_frag.o(.data)
    ip_reass_pbufcount                       0x20000060   Data           2  ip_frag.o(.data)
    reassdatagrams                           0x20000064   Data           4  ip_frag.o(.data)
    .data                                    0x20000068   Section       16  mem.o(.data)
    ram                                      0x2000006c   Data           4  mem.o(.data)
    ram_end                                  0x20000070   Data           4  mem.o(.data)
    lfree                                    0x20000074   Data           4  mem.o(.data)
    .data                                    0x20000078   Section        4  memp.o(.data)
    .data                                    0x2000007c   Section       12  netif.o(.data)
    netif_num                                0x2000007c   Data           1  netif.o(.data)
    .data                                    0x20000088   Section        4  raw.o(.data)
    raw_pcbs                                 0x20000088   Data           4  raw.o(.data)
    .data                                    0x2000008c   Section       36  tcp.o(.data)
    tcp_timer                                0x2000008d   Data           1  tcp.o(.data)
    tcp_timer_ctr                            0x2000008e   Data           1  tcp.o(.data)
    tcp_port                                 0x20000090   Data           2  tcp.o(.data)
    iss                                      0x20000094   Data           4  tcp.o(.data)
    .data                                    0x200000b0   Section       28  tcp_in.o(.data)
    flags                                    0x200000b0   Data           1  tcp_in.o(.data)
    recv_flags                               0x200000b1   Data           1  tcp_in.o(.data)
    tcplen                                   0x200000b2   Data           2  tcp_in.o(.data)
    tcphdr                                   0x200000b4   Data           4  tcp_in.o(.data)
    iphdr                                    0x200000b8   Data           4  tcp_in.o(.data)
    seqno                                    0x200000bc   Data           4  tcp_in.o(.data)
    ackno                                    0x200000c0   Data           4  tcp_in.o(.data)
    recv_data                                0x200000c4   Data           4  tcp_in.o(.data)
    .data                                    0x200000cc   Section       12  timers.o(.data)
    next_timeout                             0x200000cc   Data           4  timers.o(.data)
    timeouts_last_time                       0x200000d0   Data           4  timers.o(.data)
    tcpip_tcp_timer_active                   0x200000d4   Data           4  timers.o(.data)
    .data                                    0x200000d8   Section        8  udp.o(.data)
    udp_port                                 0x200000d8   Data           2  udp.o(.data)
    .data                                    0x200000e0   Section       36  malloc.o(.data)
    .data                                    0x20000104   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000108   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x2000010c   Data           4  tasks.o(.data)
    xTickCount                               0x20000110   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000114   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000118   Data           4  tasks.o(.data)
    uxPendedTicks                            0x2000011c   Data           4  tasks.o(.data)
    xYieldPending                            0x20000120   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000124   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000128   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x2000012c   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000130   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000134   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x20000138   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x2000013c   Data           4  tasks.o(.data)
    .data                                    0x20000140   Section       24  heap_4.o(.data)
    pxEnd                                    0x20000140   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000144   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x20000148   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x2000014c   Data           4  heap_4.o(.data)
    xStart                                   0x20000150   Data           8  heap_4.o(.data)
    .data                                    0x20000158   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x20000158   Data           1  port.o(.data)
    uxCriticalNesting                        0x2000015c   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x20000160   Data           4  port.o(.data)
    .bss                                     0x20000164   Section    15300  gd32f4xx_enet.o(.bss)
    enet_initpara                            0x20000164   Data          60  gd32f4xx_enet.o(.bss)
    .bss                                     0x20003d28   Section       72  lwip_comm.o(.bss)
    .bss                                     0x20003d70   Section      200  etharp.o(.bss)
    arp_table                                0x20003d70   Data         200  etharp.o(.bss)
    .bss                                     0x20003e38   Section       16  ip_addr.o(.bss)
    str                                      0x20003e38   Data          16  ip_addr.o(.bss)
    .bss                                     0x20003e48   Section       40  memp.o(.bss)
    memp_tab                                 0x20003e48   Data          40  memp.o(.bss)
    .bss                                     0x20003e70   Section       16  tcp_in.o(.bss)
    inseg                                    0x20003e70   Data          16  tcp_in.o(.bss)
    .bss                                     0x20003e80   Section    102400  malloc.o(.bss)
    .bss                                     0x2001ce80   Section     6400  malloc.o(.bss)
    .bss                                     0x2001e780   Section      740  tasks.o(.bss)
    pxReadyTasksLists                        0x2001e780   Data         640  tasks.o(.bss)
    xDelayedTaskList1                        0x2001ea00   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x2001ea14   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x2001ea28   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x2001ea3c   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x2001ea50   Data          20  tasks.o(.bss)
    .bss                                     0x2001ea64   Section    36864  heap_4.o(.bss)
    ucHeap                                   0x2001ea64   Data       36864  heap_4.o(.bss)
    STACK                                    0x20027a68   Section     1024  startup_gd32f450_470.o(STACK)
    .ARM.__AT_0x68000000                     0x68000000   Section    204800  malloc.o(.ARM.__AT_0x68000000)
    .ARM.__AT_0x68032000                     0x68032000   Section    12800  malloc.o(.ARM.__AT_0x68032000)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_gd32f450_470.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_gd32f450_470.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_gd32f450_470.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080001bd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x080001c1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x080001c1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    SVC_Handler                              0x080001c5   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001e5   Thumb Code    28  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvEnableVFP  0x08000205   Thumb Code    16  port.o(.emb_text)
    PendSV_Handler                           0x08000219   Thumb Code    88  port.o(.emb_text)
    vPortGetIPSR                             0x08000275   Thumb Code     6  port.o(.emb_text)
    NMI_Handler                              0x0800027b   Thumb Code     2  gd32f4xx_it.o(.text)
    HardFault_Handler                        0x0800027d   Thumb Code     2  gd32f4xx_it.o(.text)
    MemManage_Handler                        0x0800027f   Thumb Code     2  gd32f4xx_it.o(.text)
    BusFault_Handler                         0x08000281   Thumb Code     2  gd32f4xx_it.o(.text)
    UsageFault_Handler                       0x08000283   Thumb Code     2  gd32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000285   Thumb Code     2  gd32f4xx_it.o(.text)
    SysTick_Handler                          0x08000287   Thumb Code    20  gd32f4xx_it.o(.text)
    main                                     0x0800029b   Thumb Code    14  main.o(.text)
    systick_config                           0x080002a9   Thumb Code    64  systick.o(.text)
    delay_1us                                0x080002e9   Thumb Code    62  systick.o(.text)
    delay_1ms                                0x08000327   Thumb Code    62  systick.o(.text)
    LED_Init                                 0x08000375   Thumb Code    56  led.o(.text)
    KEY_Init                                 0x080003b1   Thumb Code    74  key.o(.text)
    Key_Scan                                 0x080003fb   Thumb Code    32  key.o(.text)
    KEY_Scan1                                0x0800041b   Thumb Code   154  key.o(.text)
    ETH_MACDMA_Config                        0x080004c1   Thumb Code    70  lan8720.o(.text)
    LAN8720_Init                             0x08000507   Thumb Code   350  lan8720.o(.text)
    LAN8720_Get_Speed                        0x08000665   Thumb Code    38  lan8720.o(.text)
    ENET_IRQHandler                          0x0800068b   Thumb Code    34  lan8720.o(.text)
    ETH_Rx_Packet                            0x080006ad   Thumb Code    72  lan8720.o(.text)
    ETH_Tx_Packet                            0x080006f5   Thumb Code    50  lan8720.o(.text)
    ETH_GetCurrentTxBuffer                   0x08000727   Thumb Code     8  lan8720.o(.text)
    ETH_Mem_Free                             0x0800072f   Thumb Code    26  lan8720.o(.text)
    ETH_Mem_Malloc                           0x08000749   Thumb Code    44  lan8720.o(.text)
    _sys_exit                                0x080007b5   Thumb Code     2  usart0.o(.text)
    fputc                                    0x080007b7   Thumb Code    30  usart0.o(.text)
    USART0_Config                            0x080007d5   Thumb Code   168  usart0.o(.text)
    USART0_SendData                          0x0800087d   Thumb Code    60  usart0.o(.text)
    USART0_IRQHandler                        0x080008b9   Thumb Code    40  usart0.o(.text)
    show_address                             0x080008f1   Thumb Code   218  implement.o(.text)
    Setup_task                               0x08000a5b   Thumb Code    40  implement.o(.text)
    System_Init                              0x08000a83   Thumb Code   126  implement.o(.text)
    Implement                                0x08000b01   Thumb Code     4  implement.o(.text)
    Timer3_Init                              0x08000d85   Thumb Code   102  timer.o(.text)
    TIMER3_IRQHandler                        0x08000deb   Thumb Code    36  timer.o(.text)
    SystemInit                               0x08000eb5   Thumb Code   120  system_gd32f4xx.o(.text)
    SystemCoreClockUpdate                    0x08000f2d   Thumb Code   114  system_gd32f4xx.o(.text)
    enet_initpara_reset                      0x08000fd5   Thumb Code    36  gd32f4xx_enet.o(.text)
    enet_deinit                              0x08000ff9   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_initpara_config                     0x08001011   Thumb Code   218  gd32f4xx_enet.o(.text)
    enet_phy_write_read                      0x080010eb   Thumb Code    90  gd32f4xx_enet.o(.text)
    enet_phy_config                          0x08001145   Thumb Code   154  gd32f4xx_enet.o(.text)
    enet_init                                0x080011df   Thumb Code   748  gd32f4xx_enet.o(.text)
    enet_software_reset                      0x080014cb   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_rxframe_drop                        0x080014f7   Thumb Code    82  gd32f4xx_enet.o(.text)
    enet_rxframe_size_get                    0x08001549   Thumb Code    68  gd32f4xx_enet.o(.text)
    enet_descriptors_chain_init              0x0800158d   Thumb Code   118  gd32f4xx_enet.o(.text)
    enet_descriptors_ring_init               0x08001603   Thumb Code   150  gd32f4xx_enet.o(.text)
    enet_frame_receive                       0x08001699   Thumb Code   130  gd32f4xx_enet.o(.text)
    enet_frame_transmit                      0x0800171b   Thumb Code   114  gd32f4xx_enet.o(.text)
    enet_transmit_checksum_config            0x0800178d   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_rx_enable                           0x08001799   Thumb Code    22  gd32f4xx_enet.o(.text)
    enet_txfifo_flush                        0x080017af   Thumb Code    40  gd32f4xx_enet.o(.text)
    enet_tx_enable                           0x080017d7   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_enable                              0x080017f3   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_rx_disable                          0x080017ff   Thumb Code    46  gd32f4xx_enet.o(.text)
    enet_tx_disable                          0x0800182d   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_disable                             0x08001849   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_mac_address_set                     0x08001855   Thumb Code    32  gd32f4xx_enet.o(.text)
    enet_mac_address_get                     0x08001875   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_flag_get                            0x0800189b   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_flag_clear                          0x080018b7   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_interrupt_enable                    0x080018c9   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_interrupt_disable                   0x080018ef   Thumb Code    38  gd32f4xx_enet.o(.text)
    enet_interrupt_flag_get                  0x08001915   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_interrupt_flag_clear                0x08001931   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_registers_get                       0x08001943   Thumb Code    34  gd32f4xx_enet.o(.text)
    enet_debug_status_get                    0x08001965   Thumb Code    86  gd32f4xx_enet.o(.text)
    enet_address_filter_enable               0x080019bb   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_address_filter_disable              0x080019c9   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_address_filter_config               0x080019d7   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_phyloopback_enable                  0x080019e9   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_phyloopback_disable                 0x08001a15   Thumb Code    44  gd32f4xx_enet.o(.text)
    enet_forward_feature_enable              0x08001a41   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_forward_feature_disable             0x08001a5d   Thumb Code    28  gd32f4xx_enet.o(.text)
    enet_fliter_feature_enable               0x08001a79   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_fliter_feature_disable              0x08001a83   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_pauseframe_generate                 0x08001a8d   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_pauseframe_detect_config            0x08001aa5   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_pauseframe_config                   0x08001ab7   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_flowcontrol_threshold_config        0x08001ad1   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_flowcontrol_feature_enable          0x08001add   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_flowcontrol_feature_disable         0x08001af7   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_dmaprocess_state_get                0x08001b11   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_dmaprocess_resume                   0x08001b19   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_rxprocess_check_recovery            0x08001b2b   Thumb Code    32  gd32f4xx_enet.o(.text)
    enet_current_desc_address_get            0x08001b4b   Thumb Code     6  gd32f4xx_enet.o(.text)
    enet_desc_information_get                0x08001b51   Thumb Code    80  gd32f4xx_enet.o(.text)
    enet_missed_frame_counter_get            0x08001ba1   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_desc_flag_get                       0x08001bb1   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_desc_flag_set                       0x08001bbf   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_desc_flag_clear                     0x08001bc7   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_rx_desc_immediate_receive_complete_interrupt 0x08001bcf   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_rx_desc_delay_receive_complete_interrupt 0x08001bd9   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_dma_feature_enable                  0x08001be9   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_dma_feature_disable                 0x08001bf3   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_rx_desc_enhanced_status_get         0x08001bfd   Thumb Code    52  gd32f4xx_enet.o(.text)
    enet_desc_select_enhanced_mode           0x08001c31   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_ptp_enhanced_descriptors_chain_init 0x08001c3d   Thumb Code   108  gd32f4xx_enet.o(.text)
    enet_ptp_enhanced_descriptors_ring_init  0x08001ca9   Thumb Code   134  gd32f4xx_enet.o(.text)
    enet_ptpframe_receive_enhanced_mode      0x08001d2f   Thumb Code   178  gd32f4xx_enet.o(.text)
    enet_ptpframe_transmit_enhanced_mode     0x08001de1   Thumb Code   156  gd32f4xx_enet.o(.text)
    enet_wum_filter_register_pointer_reset   0x08001e7d   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_wum_filter_config                   0x08001e89   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_wum_feature_enable                  0x08001e9b   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_wum_feature_disable                 0x08001ea5   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_msc_counters_reset                  0x08001eaf   Thumb Code    16  gd32f4xx_enet.o(.text)
    enet_msc_feature_enable                  0x08001ebf   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_msc_feature_disable                 0x08001ecd   Thumb Code    14  gd32f4xx_enet.o(.text)
    enet_msc_counters_preset_config          0x08001edb   Thumb Code    26  gd32f4xx_enet.o(.text)
    enet_msc_counters_get                    0x08001ef5   Thumb Code     6  gd32f4xx_enet.o(.text)
    enet_ptp_feature_enable                  0x08001efb   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_feature_disable                 0x08001f05   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_function_config       0x08001f0f   Thumb Code   172  gd32f4xx_enet.o(.text)
    enet_ptp_subsecond_increment_config      0x08001fbb   Thumb Code    10  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_addend_config         0x08001fc5   Thumb Code     8  gd32f4xx_enet.o(.text)
    enet_ptp_timestamp_update_config         0x08001fcd   Thumb Code    18  gd32f4xx_enet.o(.text)
    enet_ptp_expected_time_config            0x08001fdf   Thumb Code    12  gd32f4xx_enet.o(.text)
    enet_ptp_system_time_get                 0x08001feb   Thumb Code    24  gd32f4xx_enet.o(.text)
    enet_ptp_pps_output_frequency_config     0x08002003   Thumb Code     8  gd32f4xx_enet.o(.text)
    gpio_deinit                              0x08002037   Thumb Code   104  gd32f4xx_gpio.o(.text)
    gpio_mode_set                            0x0800209f   Thumb Code    72  gd32f4xx_gpio.o(.text)
    gpio_output_options_set                  0x080020e7   Thumb Code    62  gd32f4xx_gpio.o(.text)
    gpio_bit_set                             0x08002125   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_bit_reset                           0x08002129   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_bit_write                           0x0800212d   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_port_write                          0x08002137   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_input_bit_get                       0x0800213b   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_input_port_get                      0x08002145   Thumb Code     6  gd32f4xx_gpio.o(.text)
    gpio_output_bit_get                      0x0800214b   Thumb Code    10  gd32f4xx_gpio.o(.text)
    gpio_output_port_get                     0x08002155   Thumb Code     6  gd32f4xx_gpio.o(.text)
    gpio_af_set                              0x0800215b   Thumb Code    86  gd32f4xx_gpio.o(.text)
    gpio_pin_lock                            0x080021b1   Thumb Code    16  gd32f4xx_gpio.o(.text)
    gpio_bit_toggle                          0x080021c1   Thumb Code     4  gd32f4xx_gpio.o(.text)
    gpio_port_toggle                         0x080021c5   Thumb Code     8  gd32f4xx_gpio.o(.text)
    nvic_priority_group_set                  0x080021cd   Thumb Code    10  gd32f4xx_misc.o(.text)
    nvic_irq_enable                          0x080021d7   Thumb Code   144  gd32f4xx_misc.o(.text)
    nvic_irq_disable                         0x08002267   Thumb Code    22  gd32f4xx_misc.o(.text)
    nvic_vector_table_set                    0x0800227d   Thumb Code    18  gd32f4xx_misc.o(.text)
    system_lowpower_set                      0x0800228f   Thumb Code    12  gd32f4xx_misc.o(.text)
    system_lowpower_reset                    0x0800229b   Thumb Code    12  gd32f4xx_misc.o(.text)
    systick_clksource_set                    0x080022a7   Thumb Code    24  gd32f4xx_misc.o(.text)
    rcu_flag_get                             0x080022cd   Thumb Code    30  gd32f4xx_rcu.o(.text)
    rcu_osci_stab_wait                       0x080022eb   Thumb Code   232  gd32f4xx_rcu.o(.text)
    rcu_deinit                               0x080023d3   Thumb Code    96  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_enable                  0x08002433   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_disable                 0x0800244d   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_sleep_enable            0x08002467   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_clock_sleep_disable           0x08002481   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_reset_enable                  0x0800249b   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_periph_reset_disable                 0x080024b5   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_bkp_reset_enable                     0x080024cf   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_bkp_reset_disable                    0x080024dd   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_system_clock_source_config           0x080024eb   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_system_clock_source_get              0x080024fb   Thumb Code    12  gd32f4xx_rcu.o(.text)
    rcu_ahb_clock_config                     0x08002507   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_apb1_clock_config                    0x08002517   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_apb2_clock_config                    0x08002527   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_ckout0_config                        0x08002537   Thumb Code    18  gd32f4xx_rcu.o(.text)
    rcu_ckout1_config                        0x08002549   Thumb Code    18  gd32f4xx_rcu.o(.text)
    rcu_pll_config                           0x0800255b   Thumb Code   100  gd32f4xx_rcu.o(.text)
    rcu_plli2s_config                        0x080025bf   Thumb Code    36  gd32f4xx_rcu.o(.text)
    rcu_pllsai_config                        0x080025e3   Thumb Code    62  gd32f4xx_rcu.o(.text)
    rcu_rtc_clock_config                     0x08002621   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_rtc_div_config                       0x08002631   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_i2s_clock_config                     0x08002641   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_ck48m_clock_config                   0x08002651   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_pll48m_clock_config                  0x08002661   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_timer_clock_prescaler_config         0x08002671   Thumb Code    24  gd32f4xx_rcu.o(.text)
    rcu_tli_clock_div_config                 0x08002689   Thumb Code    16  gd32f4xx_rcu.o(.text)
    rcu_all_reset_flag_clear                 0x08002699   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_interrupt_flag_get                   0x080026a7   Thumb Code    58  gd32f4xx_rcu.o(.text)
    rcu_interrupt_flag_clear                 0x080026e1   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_interrupt_enable                     0x080026fb   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_interrupt_disable                    0x08002715   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_lxtal_drive_capability_config        0x0800272f   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_osci_on                              0x0800273d   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_osci_off                             0x08002757   Thumb Code    26  gd32f4xx_rcu.o(.text)
    rcu_osci_bypass_mode_enable              0x08002771   Thumb Code    60  gd32f4xx_rcu.o(.text)
    rcu_osci_bypass_mode_disable             0x080027ad   Thumb Code    60  gd32f4xx_rcu.o(.text)
    rcu_hxtal_clock_monitor_enable           0x080027e9   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_hxtal_clock_monitor_disable          0x080027f7   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_irc16m_adjust_value_set              0x08002805   Thumb Code    22  gd32f4xx_rcu.o(.text)
    rcu_voltage_key_unlock                   0x0800281b   Thumb Code    10  gd32f4xx_rcu.o(.text)
    rcu_deepsleep_voltage_set                0x08002825   Thumb Code    12  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_config               0x08002831   Thumb Code    24  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_enable               0x08002849   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_spread_spectrum_disable              0x08002857   Thumb Code    14  gd32f4xx_rcu.o(.text)
    rcu_clock_freq_get                       0x08002865   Thumb Code   194  gd32f4xx_rcu.o(.text)
    syscfg_deinit                            0x0800294d   Thumb Code    22  gd32f4xx_syscfg.o(.text)
    syscfg_bootmode_config                   0x08002963   Thumb Code    18  gd32f4xx_syscfg.o(.text)
    syscfg_fmc_swap_config                   0x08002975   Thumb Code    14  gd32f4xx_syscfg.o(.text)
    syscfg_exmc_swap_config                  0x08002983   Thumb Code    14  gd32f4xx_syscfg.o(.text)
    syscfg_exti_line_config                  0x08002991   Thumb Code    64  gd32f4xx_syscfg.o(.text)
    syscfg_enet_phy_interface_config         0x080029d1   Thumb Code    16  gd32f4xx_syscfg.o(.text)
    syscfg_compensation_config               0x080029e1   Thumb Code    16  gd32f4xx_syscfg.o(.text)
    syscfg_flag_get                          0x080029f1   Thumb Code    18  gd32f4xx_syscfg.o(.text)
    timer_deinit                             0x08002a09   Thumb Code   190  gd32f4xx_timer.o(.text)
    timer_struct_para_init                   0x08002ac7   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_init                               0x08002adb   Thumb Code   122  gd32f4xx_timer.o(.text)
    timer_enable                             0x08002b55   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_disable                            0x08002b5f   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_auto_reload_shadow_enable          0x08002b69   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_auto_reload_shadow_disable         0x08002b73   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_update_event_enable                0x08002b7d   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_update_event_disable               0x08002b87   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_counter_alignment                  0x08002b91   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_counter_up_direction               0x08002ba1   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_counter_down_direction             0x08002bab   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_prescaler_config                   0x08002bb5   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_repetition_value_config            0x08002bc5   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_autoreload_value_config            0x08002bc9   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_counter_value_config               0x08002bcd   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_counter_read                       0x08002bd1   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_prescaler_read                     0x08002bd5   Thumb Code     6  gd32f4xx_timer.o(.text)
    timer_single_pulse_mode_config           0x08002bdb   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_update_source_config               0x08002bf3   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_dma_enable                         0x08002c0b   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_dma_disable                        0x08002c13   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_channel_dma_request_source_select  0x08002c1b   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_dma_transfer_config                0x08002c33   Thumb Code    22  gd32f4xx_timer.o(.text)
    timer_event_software_generate            0x08002c49   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_break_struct_para_init             0x08002c51   Thumb Code    18  gd32f4xx_timer.o(.text)
    timer_break_config                       0x08002c63   Thumb Code    32  gd32f4xx_timer.o(.text)
    timer_break_enable                       0x08002c83   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_break_disable                      0x08002c8d   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_automatic_output_enable            0x08002c97   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_automatic_output_disable           0x08002ca1   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_primary_output_config              0x08002cab   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_channel_control_shadow_config      0x08002cbf   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_channel_control_shadow_update_config 0x08002cd3   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_channel_output_struct_para_init    0x08002ceb   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_channel_output_config              0x08002cfb   Thumb Code   490  gd32f4xx_timer.o(.text)
    timer_channel_output_mode_config         0x08002ee5   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_pulse_value_config  0x08002f33   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_channel_output_shadow_config       0x08002f51   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_fast_config         0x08002f9f   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_clear_config        0x08002fed   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_output_polarity_config     0x0800303b   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_complementary_output_polarity_config 0x08003089   Thumb Code    58  gd32f4xx_timer.o(.text)
    timer_channel_output_state_config        0x080030c3   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_channel_complementary_output_state_config 0x08003111   Thumb Code    58  gd32f4xx_timer.o(.text)
    timer_channel_input_struct_para_init     0x0800314b   Thumb Code    14  gd32f4xx_timer.o(.text)
    timer_channel_input_capture_prescaler_config 0x08003159   Thumb Code    78  gd32f4xx_timer.o(.text)
    timer_input_capture_config               0x080031a7   Thumb Code   298  gd32f4xx_timer.o(.text)
    timer_channel_capture_value_register_read 0x080032d1   Thumb Code    34  gd32f4xx_timer.o(.text)
    timer_input_pwm_capture_config           0x080032f3   Thumb Code   332  gd32f4xx_timer.o(.text)
    timer_hall_mode_config                   0x0800343f   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_input_trigger_source_select        0x08003457   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_master_output_trigger_source_select 0x08003467   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_slave_mode_select                  0x08003477   Thumb Code    16  gd32f4xx_timer.o(.text)
    timer_master_slave_mode_config           0x08003487   Thumb Code    24  gd32f4xx_timer.o(.text)
    timer_external_trigger_config            0x0800349f   Thumb Code    28  gd32f4xx_timer.o(.text)
    timer_quadrature_decoder_mode_config     0x080034bb   Thumb Code    64  gd32f4xx_timer.o(.text)
    timer_internal_clock_config              0x080034fb   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_internal_trigger_as_external_clock_config 0x08003505   Thumb Code    26  gd32f4xx_timer.o(.text)
    timer_external_trigger_as_external_clock_config 0x0800351f   Thumb Code   156  gd32f4xx_timer.o(.text)
    timer_external_clock_mode0_config        0x080035bb   Thumb Code    26  gd32f4xx_timer.o(.text)
    timer_external_clock_mode1_config        0x080035d5   Thumb Code    18  gd32f4xx_timer.o(.text)
    timer_external_clock_mode1_disable       0x080035e7   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_channel_remap_config               0x080035f1   Thumb Code     4  gd32f4xx_timer.o(.text)
    timer_write_chxval_register_config       0x080035f5   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_output_value_selection_config      0x08003613   Thumb Code    30  gd32f4xx_timer.o(.text)
    timer_flag_get                           0x08003631   Thumb Code    10  gd32f4xx_timer.o(.text)
    timer_flag_clear                         0x0800363b   Thumb Code     6  gd32f4xx_timer.o(.text)
    timer_interrupt_enable                   0x08003641   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_interrupt_disable                  0x08003649   Thumb Code     8  gd32f4xx_timer.o(.text)
    timer_interrupt_flag_get                 0x08003651   Thumb Code    20  gd32f4xx_timer.o(.text)
    timer_interrupt_flag_clear               0x08003665   Thumb Code     6  gd32f4xx_timer.o(.text)
    usart_deinit                             0x0800366d   Thumb Code   116  gd32f4xx_usart.o(.text)
    usart_baudrate_set                       0x080036e1   Thumb Code   118  gd32f4xx_usart.o(.text)
    usart_parity_config                      0x08003757   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_word_length_set                    0x08003767   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_stop_bit_set                       0x08003777   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_enable                             0x08003787   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_disable                            0x08003791   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_transmit_config                    0x0800379b   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receive_config                     0x080037a7   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_data_first_config                  0x080037b3   Thumb Code    14  gd32f4xx_usart.o(.text)
    usart_invert_config                      0x080037c1   Thumb Code    76  gd32f4xx_usart.o(.text)
    usart_oversample_config                  0x0800380d   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_sample_bit_config                  0x0800381d   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_enable            0x0800382d   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_disable           0x08003839   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_receiver_timeout_threshold_config  0x08003845   Thumb Code    18  gd32f4xx_usart.o(.text)
    usart_data_transmit                      0x08003857   Thumb Code     8  gd32f4xx_usart.o(.text)
    usart_data_receive                       0x0800385f   Thumb Code     8  gd32f4xx_usart.o(.text)
    usart_address_config                     0x08003867   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_mute_mode_enable                   0x0800387b   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_mute_mode_disable                  0x08003885   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_mute_mode_wakeup_config            0x0800388f   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_lin_mode_enable                    0x0800389f   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_lin_mode_disable                   0x080038a9   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_lin_break_detection_length_config  0x080038b3   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_send_break                         0x080038c7   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_halfduplex_enable                  0x080038d1   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_halfduplex_disable                 0x080038db   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_enable           0x080038e5   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_disable          0x080038ef   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_synchronous_clock_config           0x080038f9   Thumb Code    30  gd32f4xx_usart.o(.text)
    usart_guard_time_config                  0x08003917   Thumb Code    24  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_enable              0x0800392f   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_disable             0x08003939   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_nack_enable         0x08003943   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_mode_nack_disable        0x0800394d   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_smartcard_autoretry_config         0x08003957   Thumb Code    24  gd32f4xx_usart.o(.text)
    usart_block_length_config                0x0800396f   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_irda_mode_enable                   0x08003983   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_irda_mode_disable                  0x0800398d   Thumb Code    10  gd32f4xx_usart.o(.text)
    usart_prescaler_config                   0x08003997   Thumb Code    16  gd32f4xx_usart.o(.text)
    usart_irda_lowpower_config               0x080039a7   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_hardware_flow_rts_config           0x080039bb   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_hardware_flow_cts_config           0x080039c7   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_break_frame_coherence_config       0x080039d3   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_parity_check_coherence_config      0x080039e9   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_hardware_flow_coherence_config     0x080039ff   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_dma_receive_config                 0x08003a15   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_dma_transmit_config                0x08003a21   Thumb Code    12  gd32f4xx_usart.o(.text)
    usart_flag_get                           0x08003a2d   Thumb Code    22  gd32f4xx_usart.o(.text)
    usart_flag_clear                         0x08003a43   Thumb Code    30  gd32f4xx_usart.o(.text)
    usart_interrupt_enable                   0x08003a61   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_interrupt_disable                  0x08003a75   Thumb Code    20  gd32f4xx_usart.o(.text)
    usart_interrupt_flag_get                 0x08003a89   Thumb Code    48  gd32f4xx_usart.o(.text)
    usart_interrupt_flag_clear               0x08003ab9   Thumb Code    20  gd32f4xx_usart.o(.text)
    Reset_Handler                            0x08003acd   Thumb Code     8  startup_gd32f450_470.o(.text)
    ADC_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_EWMC_IRQHandler                     0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX0_IRQHandler                      0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_RX1_IRQHandler                      0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN0_TX_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_EWMC_IRQHandler                     0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX0_IRQHandler                      0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_RX1_IRQHandler                      0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    CAN1_TX_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DCI_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel0_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel1_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel2_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel3_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel4_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel5_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel6_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA0_Channel7_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel0_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel1_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel2_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel3_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel4_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel5_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel6_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    DMA1_Channel7_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    ENET_WKUP_IRQHandler                     0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXMC_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI0_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI10_15_IRQHandler                     0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI1_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI2_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI3_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI4_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    EXTI5_9_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    FMC_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    FPU_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_ER_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C0_EV_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_ER_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C1_EV_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_ER_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    I2C2_EV_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    IPA_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    LVD_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    RCU_CTC_IRQHandler                       0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_Alarm_IRQHandler                     0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    RTC_WKUP_IRQHandler                      0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SDIO_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI0_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI1_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI2_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI3_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI4_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    SPI5_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TAMPER_STAMP_IRQHandler                  0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_BRK_TIMER8_IRQHandler             0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_Channel_IRQHandler                0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_TRG_CMT_TIMER10_IRQHandler        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER0_UP_TIMER9_IRQHandler              0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER1_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER2_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER4_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER5_DAC_IRQHandler                    0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER6_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_BRK_TIMER11_IRQHandler            0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_Channel_IRQHandler                0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_TRG_CMT_TIMER13_IRQHandler        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TIMER7_UP_TIMER12_IRQHandler             0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_ER_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TLI_IRQHandler                           0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    TRNG_IRQHandler                          0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART3_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART4_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART6_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    UART7_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART1_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART2_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USART5_IRQHandler                        0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBFS_WKUP_IRQHandler                    0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_In_IRQHandler                  0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_EP1_Out_IRQHandler                 0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    USBHS_WKUP_IRQHandler                    0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    WWDGT_IRQHandler                         0x08003ae7   Thumb Code     0  startup_gd32f450_470.o(.text)
    lwip_comm_mem_free                       0x08003af1   Thumb Code    26  lwip_comm.o(.text)
    lwip_comm_mem_malloc                     0x08003b0b   Thumb Code    50  lwip_comm.o(.text)
    lwip_comm_default_ip_set                 0x08003b3d   Thumb Code    76  lwip_comm.o(.text)
    lwip_comm_init                           0x08003b89   Thumb Code   230  lwip_comm.o(.text)
    lwip_pkt_handle                          0x08003c6f   Thumb Code     8  lwip_comm.o(.text)
    lwip_periodic_handle                     0x08003c77   Thumb Code    46  lwip_comm.o(.text)
    etharp_tmr                               0x08003da5   Thumb Code    66  etharp.o(.text)
    etharp_cleanup_netif                     0x08004007   Thumb Code    42  etharp.o(.text)
    etharp_find_addr                         0x08004031   Thumb Code    74  etharp.o(.text)
    etharp_request                           0x0800439f   Thumb Code    32  etharp.o(.text)
    etharp_query                             0x08004413   Thumb Code   298  etharp.o(.text)
    etharp_output                            0x0800453d   Thumb Code   306  etharp.o(.text)
    ethernet_input                           0x0800466f   Thumb Code   414  etharp.o(.text)
    ethernetif_input                         0x08004841   Thumb Code   116  ethernetif.o(.text)
    ethernetif_init                          0x080048b5   Thumb Code    48  ethernetif.o(.text)
    icmp_input                               0x080049dd   Thumb Code   310  icmp.o(.text)
    icmp_dest_unreach                        0x08004b8b   Thumb Code     6  icmp.o(.text)
    icmp_time_exceeded                       0x08004b91   Thumb Code     6  icmp.o(.text)
    inet_chksum_pseudo                       0x08004d63   Thumb Code   136  inet_chksum.o(.text)
    inet_chksum_pseudo_partial               0x08004deb   Thumb Code   178  inet_chksum.o(.text)
    inet_chksum                              0x08004e9d   Thumb Code    12  inet_chksum.o(.text)
    inet_chksum_pbuf                         0x08004ea9   Thumb Code    72  inet_chksum.o(.text)
    ip_route                                 0x08004f59   Thumb Code    54  ip.o(.text)
    ip_input                                 0x08004f8f   Thumb Code   326  ip.o(.text)
    ip_output_if                             0x080050d5   Thumb Code   212  ip.o(.text)
    ip_output                                0x080051a9   Thumb Code    72  ip.o(.text)
    ip4_addr_isbroadcast                     0x0800528d   Thumb Code    46  ip_addr.o(.text)
    ip4_addr_netmask_valid                   0x080052bb   Thumb Code    38  ip_addr.o(.text)
    ipaddr_aton                              0x080052e1   Thumb Code   262  ip_addr.o(.text)
    ipaddr_addr                              0x080053e7   Thumb Code    20  ip_addr.o(.text)
    ipaddr_ntoa_r                            0x080053fb   Thumb Code   118  ip_addr.o(.text)
    ipaddr_ntoa                              0x08005471   Thumb Code     6  ip_addr.o(.text)
    ip_reass_tmr                             0x080055df   Thumb Code    40  ip_frag.o(.text)
    ip_reass                                 0x080057cd   Thumb Code   824  ip_frag.o(.text)
    ip_frag                                  0x08005b51   Thumb Code   382  ip_frag.o(.text)
    lwip_htons                               0x08005d0d   Thumb Code     4  def.o(.text)
    lwip_ntohs                               0x08005d11   Thumb Code     4  def.o(.text)
    lwip_htonl                               0x08005d15   Thumb Code     4  def.o(.text)
    lwip_ntohl                               0x08005d19   Thumb Code     4  def.o(.text)
    lwip_init                                0x08005d1d   Thumb Code    30  init.o(.text)
    mem_init                                 0x08005dd7   Thumb Code    44  mem.o(.text)
    mem_free                                 0x08005e03   Thumb Code   108  mem.o(.text)
    mem_trim                                 0x08005e6f   Thumb Code   240  mem.o(.text)
    mem_malloc                               0x08005f5f   Thumb Code   252  mem.o(.text)
    mem_calloc                               0x0800605b   Thumb Code    26  mem.o(.text)
    memp_get_memorysize                      0x0800626d   Thumb Code     6  memp.o(.text)
    memp_init                                0x08006273   Thumb Code    80  memp.o(.text)
    memp_malloc                              0x080062c3   Thumb Code    62  memp.o(.text)
    memp_free                                0x08006301   Thumb Code    40  memp.o(.text)
    netif_init                               0x080063e9   Thumb Code     2  netif.o(.text)
    netif_set_gw                             0x080063eb   Thumb Code     8  netif.o(.text)
    netif_set_netmask                        0x080063f3   Thumb Code     8  netif.o(.text)
    netif_set_ipaddr                         0x080063fb   Thumb Code    96  netif.o(.text)
    netif_set_addr                           0x0800645b   Thumb Code    30  netif.o(.text)
    netif_add                                0x08006479   Thumb Code    94  netif.o(.text)
    netif_set_default                        0x080064d7   Thumb Code     6  netif.o(.text)
    netif_set_down                           0x080064dd   Thumb Code    26  netif.o(.text)
    netif_remove                             0x080064f7   Thumb Code    68  netif.o(.text)
    netif_find                               0x0800653b   Thumb Code    58  netif.o(.text)
    netif_set_up                             0x08006575   Thumb Code    32  netif.o(.text)
    netif_set_link_up                        0x08006595   Thumb Code    32  netif.o(.text)
    netif_set_link_down                      0x080065b5   Thumb Code    18  netif.o(.text)
    pbuf_free                                0x0800663d   Thumb Code   170  pbuf.o(.text)
    pbuf_alloc                               0x080066e7   Thumb Code   460  pbuf.o(.text)
    pbuf_alloced_custom                      0x080068b3   Thumb Code   100  pbuf.o(.text)
    pbuf_realloc                             0x08006917   Thumb Code   186  pbuf.o(.text)
    pbuf_header                              0x080069d1   Thumb Code   806  pbuf.o(.text)
    pbuf_clen                                0x08006cf7   Thumb Code    18  pbuf.o(.text)
    pbuf_ref                                 0x08006d09   Thumb Code    12  pbuf.o(.text)
    pbuf_cat                                 0x08006d15   Thumb Code   108  pbuf.o(.text)
    pbuf_chain                               0x08006d81   Thumb Code    16  pbuf.o(.text)
    pbuf_dechain                             0x08006d91   Thumb Code   102  pbuf.o(.text)
    pbuf_copy                                0x08006df7   Thumb Code   258  pbuf.o(.text)
    pbuf_copy_partial                        0x08006ef9   Thumb Code   130  pbuf.o(.text)
    pbuf_take                                0x08006f7b   Thumb Code   146  pbuf.o(.text)
    pbuf_coalesce                            0x0800700d   Thumb Code    66  pbuf.o(.text)
    pbuf_get_at                              0x0800704f   Thumb Code    24  pbuf.o(.text)
    pbuf_memcmp                              0x08007067   Thumb Code   646  pbuf.o(.text)
    pbuf_memfind                             0x080072ed   Thumb Code    56  pbuf.o(.text)
    pbuf_strstr                              0x08007325   Thumb Code    50  pbuf.o(.text)
    raw_input                                0x08007359   Thumb Code   104  raw.o(.text)
    raw_bind                                 0x080073c1   Thumb Code    10  raw.o(.text)
    raw_connect                              0x080073cb   Thumb Code    10  raw.o(.text)
    raw_recv                                 0x080073d5   Thumb Code     6  raw.o(.text)
    raw_sendto                               0x080073db   Thumb Code   160  raw.o(.text)
    raw_send                                 0x0800747b   Thumb Code     4  raw.o(.text)
    raw_remove                               0x0800747f   Thumb Code    40  raw.o(.text)
    raw_new                                  0x080074a7   Thumb Code    38  raw.o(.text)
    tcp_init                                 0x08007551   Thumb Code     2  tcp.o(.text)
    tcp_seg_free                             0x08007553   Thumb Code    28  tcp.o(.text)
    tcp_segs_free                            0x0800756f   Thumb Code    18  tcp.o(.text)
    tcp_pcb_purge                            0x08007581   Thumb Code    58  tcp.o(.text)
    tcp_slowtmr                              0x080075bb   Thumb Code   754  tcp.o(.text)
    tcp_pcb_remove                           0x080078ad   Thumb Code   126  tcp.o(.text)
    tcp_close                                0x08007c59   Thumb Code    18  tcp.o(.text)
    tcp_update_rcv_ann_wnd                   0x08007c6b   Thumb Code    80  tcp.o(.text)
    tcp_recved                               0x08007cbb   Thumb Code   112  tcp.o(.text)
    tcp_recv_null                            0x08007d2b   Thumb Code    34  tcp.o(.text)
    tcp_process_refused_data                 0x08007d4d   Thumb Code   100  tcp.o(.text)
    tcp_fasttmr                              0x08007db1   Thumb Code    88  tcp.o(.text)
    tcp_tmr                                  0x08007e09   Thumb Code    30  tcp.o(.text)
    tcp_shutdown                             0x08007e27   Thumb Code    76  tcp.o(.text)
    tcp_abandon                              0x08007e73   Thumb Code   162  tcp.o(.text)
    tcp_abort                                0x08007f15   Thumb Code     4  tcp.o(.text)
    tcp_bind                                 0x0800802d   Thumb Code   136  tcp.o(.text)
    tcp_listen_with_backlog                  0x080080bb   Thumb Code   146  tcp.o(.text)
    tcp_eff_send_mss                         0x0800814d   Thumb Code    32  tcp.o(.text)
    tcp_next_iss                             0x0800816d   Thumb Code    12  tcp.o(.text)
    tcp_connect                              0x08008179   Thumb Code   266  tcp.o(.text)
    tcp_setprio                              0x08008283   Thumb Code     4  tcp.o(.text)
    tcp_alloc                                0x08008287   Thumb Code   236  tcp.o(.text)
    tcp_new                                  0x08008373   Thumb Code     4  tcp.o(.text)
    tcp_arg                                  0x08008377   Thumb Code     4  tcp.o(.text)
    tcp_recv                                 0x0800837b   Thumb Code    34  tcp.o(.text)
    tcp_sent                                 0x0800839d   Thumb Code    34  tcp.o(.text)
    tcp_err                                  0x080083bf   Thumb Code    36  tcp.o(.text)
    tcp_accept                               0x080083e3   Thumb Code     4  tcp.o(.text)
    tcp_poll                                 0x080083e7   Thumb Code    42  tcp.o(.text)
    tcp_debug_state_str                      0x08008411   Thumb Code    10  tcp.o(.text)
    tcp_input                                0x08009111   Thumb Code  1212  tcp_in.o(.text)
    tcp_enqueue_flags                        0x08009777   Thumb Code   286  tcp_out.o(.text)
    tcp_send_fin                             0x08009895   Thumb Code    68  tcp_out.o(.text)
    tcp_write                                0x080099ad   Thumb Code  1410  tcp_out.o(.text)
    tcp_send_empty_ack                       0x08009f2f   Thumb Code    72  tcp_out.o(.text)
    tcp_output                               0x0800a187   Thumb Code   440  tcp_out.o(.text)
    tcp_rst                                  0x0800a33f   Thumb Code   144  tcp_out.o(.text)
    tcp_rexmit_rto                           0x0800a3cf   Thumb Code    44  tcp_out.o(.text)
    tcp_rexmit                               0x0800a3fb   Thumb Code    82  tcp_out.o(.text)
    tcp_rexmit_fast                          0x0800a44d   Thumb Code    88  tcp_out.o(.text)
    tcp_keepalive                            0x0800a4a5   Thumb Code    60  tcp_out.o(.text)
    tcp_zero_window_probe                    0x0800a4e1   Thumb Code   220  tcp_out.o(.text)
    sys_timeout                              0x0800a5bd   Thumb Code   104  timers.o(.text)
    tcp_timer_needed                         0x0800a64b   Thumb Code    34  timers.o(.text)
    sys_timeouts_init                        0x0800a695   Thumb Code    36  timers.o(.text)
    sys_untimeout                            0x0800a6b9   Thumb Code    68  timers.o(.text)
    sys_check_timeouts                       0x0800a6fd   Thumb Code    74  timers.o(.text)
    sys_restart_timeouts                     0x0800a747   Thumb Code    12  timers.o(.text)
    udp_init                                 0x0800a801   Thumb Code     2  udp.o(.text)
    udp_input                                0x0800a803   Thumb Code   328  udp.o(.text)
    udp_bind                                 0x0800a94b   Thumb Code   192  udp.o(.text)
    udp_sendto_if                            0x0800aa0b   Thumb Code   202  udp.o(.text)
    udp_sendto                               0x0800aad5   Thumb Code    40  udp.o(.text)
    udp_send                                 0x0800aafd   Thumb Code     6  udp.o(.text)
    udp_connect                              0x0800ab03   Thumb Code    70  udp.o(.text)
    udp_disconnect                           0x0800ab49   Thumb Code    16  udp.o(.text)
    udp_recv                                 0x0800ab59   Thumb Code     6  udp.o(.text)
    udp_remove                               0x0800ab5f   Thumb Code    40  udp.o(.text)
    udp_new                                  0x0800ab87   Thumb Code    26  udp.o(.text)
    sys_now                                  0x0800ac61   Thumb Code     6  sys_arch.o(.text)
    mymemcpy                                 0x0800ac6d   Thumb Code    16  malloc.o(.text)
    mymemset                                 0x0800ac7d   Thumb Code    12  malloc.o(.text)
    my_mem_init                              0x0800ac89   Thumb Code    52  malloc.o(.text)
    my_mem_perused                           0x0800acbd   Thumb Code    48  malloc.o(.text)
    my_mem_malloc                            0x0800aced   Thumb Code   136  malloc.o(.text)
    my_mem_free                              0x0800ad75   Thumb Code    82  malloc.o(.text)
    myfree                                   0x0800adc7   Thumb Code    18  malloc.o(.text)
    mymalloc                                 0x0800add9   Thumb Code    28  malloc.o(.text)
    myrealloc                                0x0800adf5   Thumb Code    54  malloc.o(.text)
    vListInitialise                          0x0800ae35   Thumb Code    22  list.o(.text)
    vListInitialiseItem                      0x0800ae4b   Thumb Code     6  list.o(.text)
    vListInsertEnd                           0x0800ae51   Thumb Code    24  list.o(.text)
    vListInsert                              0x0800ae69   Thumb Code    48  list.o(.text)
    uxListRemove                             0x0800ae99   Thumb Code    36  list.o(.text)
    xTaskCreate                              0x0800af7b   Thumb Code    86  tasks.o(.text)
    vTaskDelete                              0x0800afd1   Thumb Code   154  tasks.o(.text)
    xTaskIncrementTick                       0x0800b06b   Thumb Code   186  tasks.o(.text)
    xTaskResumeAll                           0x0800b125   Thumb Code   182  tasks.o(.text)
    vTaskSuspendAll                          0x0800b241   Thumb Code    10  tasks.o(.text)
    vTaskDelayUntil                          0x0800b24b   Thumb Code   184  tasks.o(.text)
    vTaskDelay                               0x0800b303   Thumb Code    66  tasks.o(.text)
    eTaskGetState                            0x0800b345   Thumb Code    94  tasks.o(.text)
    uxTaskPriorityGet                        0x0800b3a3   Thumb Code    24  tasks.o(.text)
    uxTaskPriorityGetFromISR                 0x0800b3bb   Thumb Code    40  tasks.o(.text)
    vTaskPrioritySet                         0x0800b3e3   Thumb Code   176  tasks.o(.text)
    vTaskSwitchContext                       0x0800b493   Thumb Code    84  tasks.o(.text)
    vTaskSuspend                             0x0800b4e7   Thumb Code   156  tasks.o(.text)
    vTaskResume                              0x0800b5b3   Thumb Code   116  tasks.o(.text)
    xTaskResumeFromISR                       0x0800b627   Thumb Code   126  tasks.o(.text)
    vTaskStartScheduler                      0x0800b6c3   Thumb Code   122  tasks.o(.text)
    vTaskEndScheduler                        0x0800b73d   Thumb Code    24  tasks.o(.text)
    xTaskGetTickCount                        0x0800b755   Thumb Code     6  tasks.o(.text)
    xTaskGetTickCountFromISR                 0x0800b75b   Thumb Code    12  tasks.o(.text)
    uxTaskGetNumberOfTasks                   0x0800b767   Thumb Code     6  tasks.o(.text)
    pcTaskGetName                            0x0800b76d   Thumb Code    38  tasks.o(.text)
    vTaskPlaceOnEventList                    0x0800b793   Thumb Code    46  tasks.o(.text)
    vTaskPlaceOnUnorderedEventList           0x0800b7c1   Thumb Code    78  tasks.o(.text)
    xTaskRemoveFromEventList                 0x0800b80f   Thumb Code   104  tasks.o(.text)
    xTaskRemoveFromUnorderedEventList        0x0800b877   Thumb Code   116  tasks.o(.text)
    vTaskSetTimeOutState                     0x0800b8eb   Thumb Code    34  tasks.o(.text)
    xTaskCheckForTimeOut                     0x0800b90d   Thumb Code   102  tasks.o(.text)
    vTaskMissedYield                         0x0800b973   Thumb Code     8  tasks.o(.text)
    xTaskGetSchedulerState                   0x0800b97b   Thumb Code    22  tasks.o(.text)
    uxTaskResetEventItemValue                0x0800b991   Thumb Code    20  tasks.o(.text)
    ulTaskNotifyTake                         0x0800b9a5   Thumb Code    96  tasks.o(.text)
    xTaskNotifyWait                          0x0800ba05   Thumb Code   128  tasks.o(.text)
    xTaskGenericNotify                       0x0800ba85   Thumb Code   208  tasks.o(.text)
    xTaskGenericNotifyFromISR                0x0800bb55   Thumb Code   228  tasks.o(.text)
    vTaskNotifyGiveFromISR                   0x0800bc39   Thumb Code   168  tasks.o(.text)
    xTaskNotifyStateClear                    0x0800bce1   Thumb Code    44  tasks.o(.text)
    pvPortMalloc                             0x0800be91   Thumb Code   214  heap_4.o(.text)
    vPortFree                                0x0800bf67   Thumb Code    96  heap_4.o(.text)
    xPortGetFreeHeapSize                     0x0800bfc7   Thumb Code     8  heap_4.o(.text)
    xPortGetMinimumEverFreeHeapSize          0x0800bfcf   Thumb Code     8  heap_4.o(.text)
    vPortInitialiseBlocks                    0x0800bfd7   Thumb Code     2  heap_4.o(.text)
    pxPortInitialiseStack                    0x0800c07d   Thumb Code    40  port.o(.text)
    vPortSetupTimerInterrupt                 0x0800c0a5   Thumb Code    26  port.o(.text)
    xPortStartScheduler                      0x0800c0bf   Thumb Code   196  port.o(.text)
    vPortEndScheduler                        0x0800c183   Thumb Code    24  port.o(.text)
    vPortEnterCritical                       0x0800c19b   Thumb Code    52  port.o(.text)
    vPortExitCritical                        0x0800c1cf   Thumb Code    34  port.o(.text)
    xPortSysTickHandler                      0x0800c1f1   Thumb Code    40  port.o(.text)
    vPortValidateInterruptPriority           0x0800c219   Thumb Code    72  port.o(.text)
    __aeabi_memcpy                           0x0800c2b5   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800c2b5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800c2b5   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800c2d9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800c2d9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800c2d9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x0800c2e7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x0800c2e7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x0800c2e7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800c2eb   Thumb Code    18  memseta.o(.text)
    strlen                                   0x0800c2fd   Thumb Code    14  strlen.o(.text)
    memcmp                                   0x0800c30b   Thumb Code    26  memcmp.o(.text)
    __aeabi_uldivmod                         0x0800c325   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsr                             0x0800c387   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0800c387   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x0800c3a9   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0800c3a9   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x0800c3cd   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800c3cd   Thumb Code     0  llshl.o(.text)
    __decompress                             0x0800c3eb   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x0800c3eb   Thumb Code    86  __dczerorl2.o(.text)
    __0printf$8                              0x0800c441   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x0800c441   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x0800c441   Thumb Code     0  printf8.o(i.__0printf$8)
    __0sprintf$8                             0x0800c461   Thumb Code    34  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x0800c461   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x0800c461   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __scatterload_copy                       0x0800c489   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800c497   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800c499   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    ethbroadcast                             0x0800c97c   Data           6  etharp.o(.constdata)
    ethzero                                  0x0800c982   Data           6  etharp.o(.constdata)
    tcp_persist_backoff                      0x0800c9b0   Data           7  tcp.o(.constdata)
    tcp_state_str                            0x0800c9b8   Data          44  tcp.o(.constdata)
    tcp_backoff                              0x0800c9e4   Data          13  tcp.o(.constdata)
    tcp_pcb_lists                            0x0800c9f4   Data          16  tcp.o(.constdata)
    memtblsize                               0x0800ca04   Data          12  malloc.o(.constdata)
    memblksize                               0x0800ca10   Data          12  malloc.o(.constdata)
    memsize                                  0x0800ca1c   Data          12  malloc.o(.constdata)
    Region$$Table$$Base                      0x0800cb3c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800cb5c   Number         0  anon$$obj.o(Region$$Table)
    mem3base                                 0x10000000   Data       61440  malloc.o(.ARM.__AT_0x10000000)
    mem3mapbase                              0x1000f000   Data        3840  malloc.o(.ARM.__AT_0x1000F000)
    data_test                                0x2000000a   Data           2  usart0.o(.data)
    __stdout                                 0x2000000c   Data           4  usart0.o(.data)
    SystemCoreClock                          0x2000001c   Data           4  system_gd32f4xx.o(.data)
    dma_current_ptp_txdesc                   0x20000020   Data           4  gd32f4xx_enet.o(.data)
    dma_current_ptp_rxdesc                   0x20000024   Data           4  gd32f4xx_enet.o(.data)
    rxdesc_tab                               0x2000002c   Data           4  gd32f4xx_enet.o(.data)
    txdesc_tab                               0x20000030   Data           4  gd32f4xx_enet.o(.data)
    dma_current_txdesc                       0x20000034   Data           4  gd32f4xx_enet.o(.data)
    dma_current_rxdesc                       0x20000038   Data           4  gd32f4xx_enet.o(.data)
    TCPTimer                                 0x2000003c   Data           4  lwip_comm.o(.data)
    ARPTimer                                 0x20000040   Data           4  lwip_comm.o(.data)
    lwip_localtime                           0x20000044   Data           4  lwip_comm.o(.data)
    current_netif                            0x20000050   Data           4  ip.o(.data)
    current_header                           0x20000054   Data           4  ip.o(.data)
    current_iphdr_src                        0x20000058   Data           4  ip.o(.data)
    current_iphdr_dest                       0x2000005c   Data           4  ip.o(.data)
    ram_heap                                 0x20000068   Data           4  mem.o(.data)
    memp_memory                              0x20000078   Data           4  memp.o(.data)
    netif_list                               0x20000080   Data           4  netif.o(.data)
    netif_default                            0x20000084   Data           4  netif.o(.data)
    tcp_active_pcbs_changed                  0x2000008c   Data           1  tcp.o(.data)
    tcp_ticks                                0x20000098   Data           4  tcp.o(.data)
    tcp_bound_pcbs                           0x2000009c   Data           4  tcp.o(.data)
    tcp_listen_pcbs                          0x200000a0   Data           4  tcp.o(.data)
    tcp_active_pcbs                          0x200000a4   Data           4  tcp.o(.data)
    tcp_tw_pcbs                              0x200000a8   Data           4  tcp.o(.data)
    tcp_tmp_pcb                              0x200000ac   Data           4  tcp.o(.data)
    tcp_input_pcb                            0x200000c8   Data           4  tcp_in.o(.data)
    udp_pcbs                                 0x200000dc   Data           4  udp.o(.data)
    mallco_dev                               0x200000e0   Data          36  malloc.o(.data)
    pxCurrentTCB                             0x20000104   Data           4  tasks.o(.data)
    rx_buff                                  0x200001a0   Data        7620  gd32f4xx_enet.o(.bss)
    tx_buff                                  0x20001f64   Data        7620  gd32f4xx_enet.o(.bss)
    lwipdev                                  0x20003d28   Data          23  lwip_comm.o(.bss)
    lwip_netif                               0x20003d40   Data          48  lwip_comm.o(.bss)
    mem1base                                 0x20003e80   Data       102400  malloc.o(.bss)
    mem1mapbase                              0x2001ce80   Data        6400  malloc.o(.bss)
    __initial_sp                             0x20027e68   Data           0  startup_gd32f450_470.o(STACK)
    mem2base                                 0x68000000   Data       204800  malloc.o(.ARM.__AT_0x68000000)
    mem2mapbase                              0x68032000   Data       12800  malloc.o(.ARM.__AT_0x68032000)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000ccc0, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0000cb90])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000cb5c, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO         1094    RESET               startup_gd32f450_470.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         1995  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         2275    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         2278    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2280    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         2282    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         2283    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         2290    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2285    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         2287    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x080001c0   0x080001c0   0x00000004   Code   RO         2276    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c4   0x080001c4   0x000000b6   Code   RO         1958    .emb_text           port.o
    0x0800027a   0x0800027a   0x00000020   Code   RO            3    .text               gd32f4xx_it.o
    0x0800029a   0x0800029a   0x0000000e   Code   RO          258    .text               main.o
    0x080002a8   0x080002a8   0x000000cc   Code   RO          278    .text               systick.o
    0x08000374   0x08000374   0x0000003c   Code   RO          300    .text               led.o
    0x080003b0   0x080003b0   0x00000110   Code   RO          320    .text               key.o
    0x080004c0   0x080004c0   0x000002f4   Code   RO          341    .text               lan8720.o
    0x080007b4   0x080007b4   0x0000013c   Code   RO          394    .text               usart0.o
    0x080008f0   0x080008f0   0x00000494   Code   RO          422    .text               implement.o
    0x08000d84   0x08000d84   0x00000094   Code   RO          446    .text               timer.o
    0x08000e18   0x08000e18   0x000001bc   Code   RO          471    .text               system_gd32f4xx.o
    0x08000fd4   0x08000fd4   0x00001062   Code   RO          658    .text               gd32f4xx_enet.o
    0x08002036   0x08002036   0x00000196   Code   RO          768    .text               gd32f4xx_gpio.o
    0x080021cc   0x080021cc   0x00000100   Code   RO          848    .text               gd32f4xx_misc.o
    0x080022cc   0x080022cc   0x00000680   Code   RO          891    .text               gd32f4xx_rcu.o
    0x0800294c   0x0800294c   0x000000bc   Code   RO          973    .text               gd32f4xx_syscfg.o
    0x08002a08   0x08002a08   0x00000c62   Code   RO          993    .text               gd32f4xx_timer.o
    0x0800366a   0x0800366a   0x00000002   PAD
    0x0800366c   0x0800366c   0x00000460   Code   RO         1054    .text               gd32f4xx_usart.o
    0x08003acc   0x08003acc   0x00000024   Code   RO         1095    .text               startup_gd32f450_470.o
    0x08003af0   0x08003af0   0x00000294   Code   RO         1101    .text               lwip_comm.o
    0x08003d84   0x08003d84   0x00000a88   Code   RO         1169    .text               etharp.o
    0x0800480c   0x0800480c   0x000001d0   Code   RO         1220    .text               ethernetif.o
    0x080049dc   0x080049dc   0x00000330   Code   RO         1256    .text               icmp.o
    0x08004d0c   0x08004d0c   0x0000024c   Code   RO         1290    .text               inet_chksum.o
    0x08004f58   0x08004f58   0x00000334   Code   RO         1305    .text               ip.o
    0x0800528c   0x0800528c   0x00000254   Code   RO         1349    .text               ip_addr.o
    0x080054e0   0x080054e0   0x0000082c   Code   RO         1373    .text               ip_frag.o
    0x08005d0c   0x08005d0c   0x00000010   Code   RO         1390    .text               def.o
    0x08005d1c   0x08005d1c   0x0000001e   Code   RO         1415    .text               init.o
    0x08005d3a   0x08005d3a   0x00000002   PAD
    0x08005d3c   0x08005d3c   0x00000530   Code   RO         1445    .text               mem.o
    0x0800626c   0x0800626c   0x0000017c   Code   RO         1460    .text               memp.o
    0x080063e8   0x080063e8   0x00000254   Code   RO         1486    .text               netif.o
    0x0800663c   0x0800663c   0x00000d1a   Code   RO         1509    .text               pbuf.o
    0x08007356   0x08007356   0x00000002   PAD
    0x08007358   0x08007358   0x000001f8   Code   RO         1522    .text               raw.o
    0x08007550   0x08007550   0x00000ff0   Code   RO         1542    .text               tcp.o
    0x08008540   0x08008540   0x0000111c   Code   RO         1567    .text               tcp_in.o
    0x0800965c   0x0800965c   0x00000f60   Code   RO         1590    .text               tcp_out.o
    0x0800a5bc   0x0800a5bc   0x00000244   Code   RO         1603    .text               timers.o
    0x0800a800   0x0800a800   0x00000460   Code   RO         1617    .text               udp.o
    0x0800ac60   0x0800ac60   0x0000000c   Code   RO         1644    .text               sys_arch.o
    0x0800ac6c   0x0800ac6c   0x000001c8   Code   RO         1729    .text               malloc.o
    0x0800ae34   0x0800ae34   0x00000088   Code   RO         1831    .text               list.o
    0x0800aebc   0x0800aebc   0x00000f8c   Code   RO         1881    .text               tasks.o
    0x0800be48   0x0800be48   0x00000210   Code   RO         1935    .text               heap_4.o
    0x0800c058   0x0800c058   0x0000025c   Code   RO         1959    .text               port.o
    0x0800c2b4   0x0800c2b4   0x00000024   Code   RO         1998    .text               mc_w.l(memcpya.o)
    0x0800c2d8   0x0800c2d8   0x00000024   Code   RO         2000    .text               mc_w.l(memseta.o)
    0x0800c2fc   0x0800c2fc   0x0000000e   Code   RO         2002    .text               mc_w.l(strlen.o)
    0x0800c30a   0x0800c30a   0x0000001a   Code   RO         2004    .text               mc_w.l(memcmp.o)
    0x0800c324   0x0800c324   0x00000062   Code   RO         2293    .text               mc_w.l(uldiv.o)
    0x0800c386   0x0800c386   0x00000020   Code   RO         2295    .text               mc_w.l(llushr.o)
    0x0800c3a6   0x0800c3a6   0x00000002   PAD
    0x0800c3a8   0x0800c3a8   0x00000024   Code   RO         2308    .text               mc_w.l(init.o)
    0x0800c3cc   0x0800c3cc   0x0000001e   Code   RO         2310    .text               mc_w.l(llshl.o)
    0x0800c3ea   0x0800c3ea   0x00000056   Code   RO         2322    .text               mc_w.l(__dczerorl2.o)
    0x0800c440   0x0800c440   0x00000020   Code   RO         2215    i.__0printf$8       mc_w.l(printf8.o)
    0x0800c460   0x0800c460   0x00000028   Code   RO         2217    i.__0sprintf$8      mc_w.l(printf8.o)
    0x0800c488   0x0800c488   0x0000000e   Code   RO         2316    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800c496   0x0800c496   0x00000002   Code   RO         2317    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800c498   0x0800c498   0x0000000e   Code   RO         2318    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800c4a6   0x0800c4a6   0x00000002   PAD
    0x0800c4a8   0x0800c4a8   0x00000404   Code   RO         2222    i._printf_core      mc_w.l(printf8.o)
    0x0800c8ac   0x0800c8ac   0x00000024   Code   RO         2223    i._printf_post_padding  mc_w.l(printf8.o)
    0x0800c8d0   0x0800c8d0   0x0000002e   Code   RO         2224    i._printf_pre_padding  mc_w.l(printf8.o)
    0x0800c8fe   0x0800c8fe   0x0000000a   Code   RO         2226    i._sputc            mc_w.l(printf8.o)
    0x0800c908   0x0800c908   0x00000074   Data   RO          660    .constdata          gd32f4xx_enet.o
    0x0800c97c   0x0800c97c   0x0000000c   Data   RO         1171    .constdata          etharp.o
    0x0800c988   0x0800c988   0x00000028   Data   RO         1462    .constdata          memp.o
    0x0800c9b0   0x0800c9b0   0x00000054   Data   RO         1543    .constdata          tcp.o
    0x0800ca04   0x0800ca04   0x00000024   Data   RO         1736    .constdata          malloc.o
    0x0800ca28   0x0800ca28   0x00000044   Data   RO         1172    .conststring        etharp.o
    0x0800ca6c   0x0800ca6c   0x00000075   Data   RO         1544    .conststring        tcp.o
    0x0800cae1   0x0800cae1   0x00000003   PAD
    0x0800cae4   0x0800cae4   0x00000055   Data   RO         1591    .conststring        tcp_out.o
    0x0800cb39   0x0800cb39   0x00000003   PAD
    0x0800cb3c   0x0800cb3c   0x00000020   Data   RO         2314    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800cb5c, Size: 0x00027e68, Max: 0x00070000, ABSOLUTE, COMPRESSED[0x00000034])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000008   Data   RW          279    .data               systick.o
    0x20000008   COMPRESSED   0x00000001   Data   RW          321    .data               key.o
    0x20000009   COMPRESSED   0x00000001   PAD
    0x2000000a   COMPRESSED   0x00000002   Data   RW          395    .data               usart0.o
    0x2000000c   COMPRESSED   0x00000004   Data   RW          396    .data               usart0.o
    0x20000010   COMPRESSED   0x0000000c   Data   RW          424    .data               implement.o
    0x2000001c   COMPRESSED   0x00000004   Data   RW          472    .data               system_gd32f4xx.o
    0x20000020   COMPRESSED   0x0000001c   Data   RW          661    .data               gd32f4xx_enet.o
    0x2000003c   COMPRESSED   0x0000000c   Data   RW         1103    .data               lwip_comm.o
    0x20000048   COMPRESSED   0x00000001   Data   RW         1173    .data               etharp.o
    0x20000049   COMPRESSED   0x00000003   PAD
    0x2000004c   COMPRESSED   0x00000014   Data   RW         1306    .data               ip.o
    0x20000060   COMPRESSED   0x00000008   Data   RW         1374    .data               ip_frag.o
    0x20000068   COMPRESSED   0x00000010   Data   RW         1446    .data               mem.o
    0x20000078   COMPRESSED   0x00000004   Data   RW         1463    .data               memp.o
    0x2000007c   COMPRESSED   0x0000000c   Data   RW         1487    .data               netif.o
    0x20000088   COMPRESSED   0x00000004   Data   RW         1523    .data               raw.o
    0x2000008c   COMPRESSED   0x00000024   Data   RW         1545    .data               tcp.o
    0x200000b0   COMPRESSED   0x0000001c   Data   RW         1569    .data               tcp_in.o
    0x200000cc   COMPRESSED   0x0000000c   Data   RW         1604    .data               timers.o
    0x200000d8   COMPRESSED   0x00000008   Data   RW         1618    .data               udp.o
    0x200000e0   COMPRESSED   0x00000024   Data   RW         1737    .data               malloc.o
    0x20000104   COMPRESSED   0x0000003c   Data   RW         1883    .data               tasks.o
    0x20000140   COMPRESSED   0x00000018   Data   RW         1937    .data               heap_4.o
    0x20000158   COMPRESSED   0x0000000c   Data   RW         1960    .data               port.o
    0x20000164        -       0x00003bc4   Zero   RW          659    .bss                gd32f4xx_enet.o
    0x20003d28        -       0x00000048   Zero   RW         1102    .bss                lwip_comm.o
    0x20003d70        -       0x000000c8   Zero   RW         1170    .bss                etharp.o
    0x20003e38        -       0x00000010   Zero   RW         1350    .bss                ip_addr.o
    0x20003e48        -       0x00000028   Zero   RW         1461    .bss                memp.o
    0x20003e70        -       0x00000010   Zero   RW         1568    .bss                tcp_in.o
    0x20003e80        -       0x00019000   Zero   RW         1734    .bss                malloc.o
    0x2001ce80        -       0x00001900   Zero   RW         1735    .bss                malloc.o
    0x2001e780        -       0x000002e4   Zero   RW         1882    .bss                tasks.o
    0x2001ea64        -       0x00009000   Zero   RW         1936    .bss                heap_4.o
    0x20027a64   COMPRESSED   0x00000004   PAD
    0x20027a68        -       0x00000400   Zero   RW         1092    STACK               startup_gd32f450_470.o



  Load Region LR$$.ARM.__AT_0x10000000 (Base: 0x10000000, Size: 0x00000000, Max: 0x0000f000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x10000000 (Exec base: 0x10000000, Load base: 0x10000000, Size: 0x0000f000, Max: 0x0000f000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x10000000        -       0x0000f000   Zero   RW         1730    .ARM.__AT_0x10000000  malloc.o



  Load Region LR$$.ARM.__AT_0x1000F000 (Base: 0x1000f000, Size: 0x00000000, Max: 0x00000f00, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x1000F000 (Exec base: 0x1000f000, Load base: 0x1000f000, Size: 0x00000f00, Max: 0x00000f00, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x1000f000        -       0x00000f00   Zero   RW         1731    .ARM.__AT_0x1000F000  malloc.o



  Load Region LR$$.ARM.__AT_0x68000000 (Base: 0x68000000, Size: 0x00000000, Max: 0x00032000, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68000000 (Exec base: 0x68000000, Load base: 0x68000000, Size: 0x00032000, Max: 0x00032000, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68000000        -       0x00032000   Zero   RW         1732    .ARM.__AT_0x68000000  malloc.o



  Load Region LR$$.ARM.__AT_0x68032000 (Base: 0x68032000, Size: 0x00000000, Max: 0x00003200, ABSOLUTE)

    Execution Region ER$$.ARM.__AT_0x68032000 (Exec base: 0x68032000, Load base: 0x68032000, Size: 0x00003200, Max: 0x00003200, ABSOLUTE, UNINIT)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x68032000        -       0x00003200   Zero   RW         1733    .ARM.__AT_0x68032000  malloc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        16          0          0          0          0       1572   def.o
      2696        584         80          1        200      17376   etharp.o
       464        122          0          0          0       2659   ethernetif.o
      4194        180        116         28      15300      62383   gd32f4xx_enet.o
       406         10          0          0          0       4382   gd32f4xx_gpio.o
        32          0          0          0          0     125546   gd32f4xx_it.o
       256         14          0          0          0       2707   gd32f4xx_misc.o
      1664         66          0          0          0      11551   gd32f4xx_rcu.o
       188          6          0          0          0       2382   gd32f4xx_syscfg.o
      3170         32          0          0          0      19612   gd32f4xx_timer.o
      1120         16          0          0          0      12132   gd32f4xx_usart.o
       528         62          0         24      36864       3740   heap_4.o
       816        374          0          0          0       2590   icmp.o
      1172        640          0         12          0       1947   implement.o
         0          0          0          0          0       3888   inet.o
       588        104          0          0          0       3521   inet_chksum.o
        30          0          0          0          0        600   init.o
       820        156          0         20          0      16304   ip.o
       596        112          0          0         16       9439   ip_addr.o
      2092        464          0          8          0       8240   ip_frag.o
       272         12          0          1          0       1250   key.o
       756         64          0          0          0       3196   lan8720.o
        60          4          0          0          0        567   led.o
       136          0          0          0          0       2074   list.o
       660        224          0         12         72       6936   lwip_comm.o
        14          0          0          0          0        547   main.o
       456         10         36         36     391680       5399   malloc.o
      1328        504          0         16          0       4198   mem.o
       380        192         40          4         40       3200   memp.o
       596        118          0         12          0       6875   netif.o
      3354       1232          0          0          0       9967   pbuf.o
       786        100          0         12          0       8533   port.o
       504        132          0          4          0       3765   raw.o
        36          8        428          0       1024       1012   startup_gd32f450_470.o
        12          6          0          0          0        538   sys_arch.o
       444         54          0          4          0       2613   system_gd32f4xx.o
       204         16          0          8          0       1301   systick.o
      3980        170          0         60        740      29298   tasks.o
      4080        994        201         36          0      23045   tcp.o
      4380        810          0         28         16      15475   tcp_in.o
      3936        862         85          0          0      11103   tcp_out.o
       148         10          0          0          0        872   timer.o
       580        174          0         12          0       2987   timers.o
      1120        192          0          8          0       7548   udp.o
       316         16          0          6          0       3470   usart0.o

    ----------------------------------------------------------------------
     49392       <USER>       <GROUP>        356     445956     468340   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          6          4          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      1192         60          0          0          0        504   printf8.o
        14          0          0          0          0         68   strlen.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1644         <USER>          <GROUP>          0          0       1124   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1640         76          0          0          0       1124   mc_w.l

    ----------------------------------------------------------------------
      1644         <USER>          <GROUP>          0          0       1124   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     51036       8922       1024        356     445956     459228   Grand Totals
     51036       8922       1024         52     445956     459228   ELF Image Totals (compressed)
     51036       8922       1024         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                52060 (  50.84kB)
    Total RW  Size (RW Data + ZI Data)            446312 ( 435.85kB)
    Total ROM Size (Code + RO Data + RW Data)      52112 (  50.89kB)

==============================================================================

