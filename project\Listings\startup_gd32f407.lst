


ARM Macro Assembler    Page 1 


    1 00000000         ;/*!
    2 00000000         ;    \file    startup_gd32f407.s
    3 00000000         ;    \brief   start up file
    4 00000000         ;
    5 00000000         ;    \version 2016-08-15, V1.0.0, firmware for GD32F4xx
    6 00000000         ;    \version 2018-12-12, V2.0.0, firmware for GD32F4xx
    7 00000000         ;    \version 2020-09-30, V2.1.0, firmware for GD32F4xx
    8 00000000         ;*/
    9 00000000         ;
   10 00000000         ;/*
   11 00000000         ;    Copyright (c) 2020, GigaDevice Semiconductor Inc.
   12 00000000         ;
   13 00000000         ;    Redistribution and use in source and binary forms, 
                       with or without modification, 
   14 00000000         ;are permitted provided that the following conditions ar
                       e met:
   15 00000000         ;
   16 00000000         ;    1. Redistributions of source code must retain the a
                       bove copyright notice, this 
   17 00000000         ;       list of conditions and the following disclaimer.
                       
   18 00000000         ;    2. Redistributions in binary form must reproduce th
                       e above copyright notice, 
   19 00000000         ;       this list of conditions and the following discla
                       imer in the documentation 
   20 00000000         ;       and/or other materials provided with the distrib
                       ution.
   21 00000000         ;    3. Neither the name of the copyright holder nor the
                        names of its contributors 
   22 00000000         ;       may be used to endorse or promote products deriv
                       ed from this software without 
   23 00000000         ;       specific prior written permission.
   24 00000000         ;
   25 00000000         ;    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS 
                       AND CONTRIBUTORS "AS IS" 
   26 00000000         ;AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT N
                       OT LIMITED TO, THE IMPLIED 
   27 00000000         ;WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICU
                       LAR PURPOSE ARE DISCLAIMED. 
   28 00000000         ;IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS 
                       BE LIABLE FOR ANY DIRECT, 
   29 00000000         ;INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENT
                       IAL DAMAGES (INCLUDING, BUT 
   30 00000000         ;NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERV
                       ICES; LOSS OF USE, DATA, OR 
   31 00000000         ;PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND O
                       N ANY THEORY OF LIABILITY, 
   32 00000000         ;WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDI
                       NG NEGLIGENCE OR OTHERWISE) 
   33 00000000         ;ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVE
                       N IF ADVISED OF THE POSSIBILITY 
   34 00000000         ;OF SUCH DAMAGE.
   35 00000000         ;*/
   36 00000000         
   37 00000000         ; <h> Stack Configuration
   38 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   39 00000000         ; </h>
   40 00000000         
   41 00000000 00000400 



ARM Macro Assembler    Page 2 


                       Stack_Size
                               EQU              0x00000400
   42 00000000         
   43 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   44 00000000         Stack_Mem
                               SPACE            Stack_Size
   45 00000400         __initial_sp
   46 00000400         
   47 00000400         
   48 00000400         ; <h> Heap Configuration
   49 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   50 00000400         ; </h>
   51 00000400         
   52 00000400 00000400 
                       Heap_Size
                               EQU              0x00000400
   53 00000400         
   54 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   55 00000000         __heap_base
   56 00000000         Heap_Mem
                               SPACE            Heap_Size
   57 00000400         __heap_limit
   58 00000400         
   59 00000400                 PRESERVE8
   60 00000400                 THUMB
   61 00000400         
   62 00000400         ;               /* reset Vector Mapped to at Address 0 *
                       /
   63 00000400                 AREA             RESET, DATA, READONLY
   64 00000000                 EXPORT           __Vectors
   65 00000000                 EXPORT           __Vectors_End
   66 00000000                 EXPORT           __Vectors_Size
   67 00000000         
   68 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   69 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   70 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   71 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   72 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   73 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   74 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   75 0000001C 00000000        DCD              0           ; Reserved
   76 00000020 00000000        DCD              0           ; Reserved
   77 00000024 00000000        DCD              0           ; Reserved
   78 00000028 00000000        DCD              0           ; Reserved
   79 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   80 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   81 00000034 00000000        DCD              0           ; Reserved
   82 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler



ARM Macro Assembler    Page 3 


                                                            
   83 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   84 00000040         
   85 00000040         ;               /* external interrupts handler */
   86 00000040 00000000        DCD              WWDGT_IRQHandler ; 16:Window Wa
                                                            tchdog Timer
   87 00000044 00000000        DCD              LVD_IRQHandler ; 17:LVD through
                                                             EXTI Line detect
   88 00000048 00000000        DCD              TAMPER_STAMP_IRQHandler ; 18:Ta
                                                            mper and TimeStamp 
                                                            through EXTI Line d
                                                            etect
   89 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; 19:RTC Wa
                                                            keup through EXTI L
                                                            ine
   90 00000050 00000000        DCD              FMC_IRQHandler ; 20:FMC
   91 00000054 00000000        DCD              RCU_CTC_IRQHandler 
                                                            ; 21:RCU and CTC
   92 00000058 00000000        DCD              EXTI0_IRQHandler 
                                                            ; 22:EXTI Line 0
   93 0000005C 00000000        DCD              EXTI1_IRQHandler 
                                                            ; 23:EXTI Line 1
   94 00000060 00000000        DCD              EXTI2_IRQHandler 
                                                            ; 24:EXTI Line 2
   95 00000064 00000000        DCD              EXTI3_IRQHandler 
                                                            ; 25:EXTI Line 3
   96 00000068 00000000        DCD              EXTI4_IRQHandler 
                                                            ; 26:EXTI Line 4
   97 0000006C 00000000        DCD              DMA0_Channel0_IRQHandler 
                                                            ; 27:DMA0 Channel0
   98 00000070 00000000        DCD              DMA0_Channel1_IRQHandler 
                                                            ; 28:DMA0 Channel1
   99 00000074 00000000        DCD              DMA0_Channel2_IRQHandler 
                                                            ; 29:DMA0 Channel2
  100 00000078 00000000        DCD              DMA0_Channel3_IRQHandler 
                                                            ; 30:DMA0 Channel3
  101 0000007C 00000000        DCD              DMA0_Channel4_IRQHandler 
                                                            ; 31:DMA0 Channel4
  102 00000080 00000000        DCD              DMA0_Channel5_IRQHandler 
                                                            ; 32:DMA0 Channel5
  103 00000084 00000000        DCD              DMA0_Channel6_IRQHandler 
                                                            ; 33:DMA0 Channel6
  104 00000088 00000000        DCD              ADC_IRQHandler ; 34:ADC
  105 0000008C 00000000        DCD              CAN0_TX_IRQHandler ; 35:CAN0 TX
                                                            
  106 00000090 00000000        DCD              CAN0_RX0_IRQHandler 
                                                            ; 36:CAN0 RX0
  107 00000094 00000000        DCD              CAN0_RX1_IRQHandler 
                                                            ; 37:CAN0 RX1
  108 00000098 00000000        DCD              CAN0_EWMC_IRQHandler 
                                                            ; 38:CAN0 EWMC
  109 0000009C 00000000        DCD              EXTI5_9_IRQHandler 
                                                            ; 39:EXTI5 to EXTI9
                                                            
  110 000000A0 00000000        DCD              TIMER0_BRK_TIMER8_IRQHandler ; 
                                                            40:TIMER0 Break and
                                                             TIMER8
  111 000000A4 00000000        DCD              TIMER0_UP_TIMER9_IRQHandler ; 4



ARM Macro Assembler    Page 4 


                                                            1:TIMER0 Update and
                                                             TIMER9
  112 000000A8 00000000        DCD              TIMER0_TRG_CMT_TIMER10_IRQHandl
er 
                                                            ; 42:TIMER0 Trigger
                                                             and Commutation an
                                                            d TIMER10
  113 000000AC 00000000        DCD              TIMER0_Channel_IRQHandler ; 43:
                                                            TIMER0 Channel Capt
                                                            ure Compare
  114 000000B0 00000000        DCD              TIMER1_IRQHandler ; 44:TIMER1
  115 000000B4 00000000        DCD              TIMER2_IRQHandler ; 45:TIMER2
  116 000000B8 00000000        DCD              TIMER3_IRQHandler ; 46:TIMER3
  117 000000BC 00000000        DCD              I2C0_EV_IRQHandler 
                                                            ; 47:I2C0 Event
  118 000000C0 00000000        DCD              I2C0_ER_IRQHandler 
                                                            ; 48:I2C0 Error
  119 000000C4 00000000        DCD              I2C1_EV_IRQHandler 
                                                            ; 49:I2C1 Event
  120 000000C8 00000000        DCD              I2C1_ER_IRQHandler 
                                                            ; 50:I2C1 Error
  121 000000CC 00000000        DCD              SPI0_IRQHandler ; 51:SPI0
  122 000000D0 00000000        DCD              SPI1_IRQHandler ; 52:SPI1
  123 000000D4 00000000        DCD              USART0_IRQHandler ; 53:USART0
  124 000000D8 00000000        DCD              USART1_IRQHandler ; 54:USART1
  125 000000DC 00000000        DCD              USART2_IRQHandler ; 55:USART2
  126 000000E0 00000000        DCD              EXTI10_15_IRQHandler ; 56:EXTI1
                                                            0 to EXTI15
  127 000000E4 00000000        DCD              RTC_Alarm_IRQHandler 
                                                            ; 57:RTC Alarm
  128 000000E8 00000000        DCD              USBFS_WKUP_IRQHandler 
                                                            ; 58:USBFS Wakeup
  129 000000EC 00000000        DCD              TIMER7_BRK_TIMER11_IRQHandler ;
                                                             59:TIMER7 Break an
                                                            d TIMER11
  130 000000F0 00000000        DCD              TIMER7_UP_TIMER12_IRQHandler ; 
                                                            60:TIMER7 Update an
                                                            d TIMER12
  131 000000F4 00000000        DCD              TIMER7_TRG_CMT_TIMER13_IRQHandl
er 
                                                            ; 61:TIMER7 Trigger
                                                             and Commutation an
                                                            d TIMER13
  132 000000F8 00000000        DCD              TIMER7_Channel_IRQHandler ; 62:
                                                            TIMER7 Capture Comp
                                                            are
  133 000000FC 00000000        DCD              DMA0_Channel7_IRQHandler 
                                                            ; 63:DMA0 Channel7
  134 00000100 00000000        DCD              EXMC_IRQHandler ; 64:EXMC
  135 00000104 00000000        DCD              SDIO_IRQHandler ; 65:SDIO
  136 00000108 00000000        DCD              TIMER4_IRQHandler ; 66:TIMER4
  137 0000010C 00000000        DCD              SPI2_IRQHandler ; 67:SPI2
  138 00000110 00000000        DCD              UART3_IRQHandler ; 68:UART3
  139 00000114 00000000        DCD              UART4_IRQHandler ; 69:UART4
  140 00000118 00000000        DCD              TIMER5_DAC_IRQHandler ; 70:TIME
                                                            R5 and DAC0 DAC1 Un
                                                            derrun error
  141 0000011C 00000000        DCD              TIMER6_IRQHandler ; 71:TIMER6
  142 00000120 00000000        DCD              DMA1_Channel0_IRQHandler 



ARM Macro Assembler    Page 5 


                                                            ; 72:DMA1 Channel0
  143 00000124 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; 73:DMA1 Channel1
  144 00000128 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; 74:DMA1 Channel2
  145 0000012C 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; 75:DMA1 Channel3
  146 00000130 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; 76:DMA1 Channel4
  147 00000134 00000000        DCD              ENET_IRQHandler ; 77:Ethernet
  148 00000138 00000000        DCD              ENET_WKUP_IRQHandler ; 78:Ether
                                                            net Wakeup through 
                                                            EXTI Line
  149 0000013C 00000000        DCD              CAN1_TX_IRQHandler ; 79:CAN1 TX
                                                            
  150 00000140 00000000        DCD              CAN1_RX0_IRQHandler 
                                                            ; 80:CAN1 RX0
  151 00000144 00000000        DCD              CAN1_RX1_IRQHandler 
                                                            ; 81:CAN1 RX1
  152 00000148 00000000        DCD              CAN1_EWMC_IRQHandler 
                                                            ; 82:CAN1 EWMC
  153 0000014C 00000000        DCD              USBFS_IRQHandler ; 83:USBFS
  154 00000150 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; 84:DMA1 Channel5
  155 00000154 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; 85:DMA1 Channel6
  156 00000158 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; 86:DMA1 Channel7
  157 0000015C 00000000        DCD              USART5_IRQHandler ; 87:USART5
  158 00000160 00000000        DCD              I2C2_EV_IRQHandler 
                                                            ; 88:I2C2 Event
  159 00000164 00000000        DCD              I2C2_ER_IRQHandler 
                                                            ; 89:I2C2 Error
  160 00000168 00000000        DCD              USBHS_EP1_Out_IRQHandler ; 90:U
                                                            SBHS Endpoint 1 Out
                                                             
  161 0000016C 00000000        DCD              USBHS_EP1_In_IRQHandler ; 91:US
                                                            BHS Endpoint 1 in
  162 00000170 00000000        DCD              USBHS_WKUP_IRQHandler ; 92:USBH
                                                            S Wakeup through EX
                                                            TI Line
  163 00000174 00000000        DCD              USBHS_IRQHandler ; 93:USBHS
  164 00000178 00000000        DCD              DCI_IRQHandler ; 94:DCI
  165 0000017C 00000000        DCD              0           ; 95:Reserved
  166 00000180 00000000        DCD              TRNG_IRQHandler ; 96:TRNG
  167 00000184 00000000        DCD              FPU_IRQHandler ; 97:FPU
  168 00000188         
  169 00000188         __Vectors_End
  170 00000188         
  171 00000188 00000188 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  172 00000188         
  173 00000188                 AREA             |.text|, CODE, READONLY
  174 00000000         
  175 00000000         ;/* reset Handler */
  176 00000000         Reset_Handler
                               PROC
  177 00000000                 EXPORT           Reset_Handler                  



ARM Macro Assembler    Page 6 


   [WEAK]
  178 00000000                 IMPORT           SystemInit
  179 00000000                 IMPORT           __main
  180 00000000 4809            LDR              R0, =SystemInit
  181 00000002 4780            BLX              R0
  182 00000004 4809            LDR              R0, =__main
  183 00000006 4700            BX               R0
  184 00000008                 ENDP
  185 00000008         
  186 00000008         ;/* dummy Exception Handlers */
  187 00000008         NMI_Handler
                               PROC
  188 00000008                 EXPORT           NMI_Handler                    
   [WEAK]
  189 00000008 E7FE            B                .
  190 0000000A                 ENDP
  192 0000000A         HardFault_Handler
                               PROC
  193 0000000A                 EXPORT           HardFault_Handler              
   [WEAK]
  194 0000000A E7FE            B                .
  195 0000000C                 ENDP
  197 0000000C         MemManage_Handler
                               PROC
  198 0000000C                 EXPORT           MemManage_Handler              
   [WEAK]
  199 0000000C E7FE            B                .
  200 0000000E                 ENDP
  202 0000000E         BusFault_Handler
                               PROC
  203 0000000E                 EXPORT           BusFault_Handler               
   [WEAK]
  204 0000000E E7FE            B                .
  205 00000010                 ENDP
  207 00000010         UsageFault_Handler
                               PROC
  208 00000010                 EXPORT           UsageFault_Handler             
   [WEAK]
  209 00000010 E7FE            B                .
  210 00000012                 ENDP
  211 00000012         SVC_Handler
                               PROC
  212 00000012                 EXPORT           SVC_Handler                    
   [WEAK]
  213 00000012 E7FE            B                .
  214 00000014                 ENDP
  216 00000014         DebugMon_Handler
                               PROC
  217 00000014                 EXPORT           DebugMon_Handler               
   [WEAK]
  218 00000014 E7FE            B                .
  219 00000016                 ENDP
  221 00000016         PendSV_Handler
                               PROC
  222 00000016                 EXPORT           PendSV_Handler                 
   [WEAK]
  223 00000016 E7FE            B                .
  224 00000018                 ENDP
  226 00000018         SysTick_Handler



ARM Macro Assembler    Page 7 


                               PROC
  227 00000018                 EXPORT           SysTick_Handler                
   [WEAK]
  228 00000018 E7FE            B                .
  229 0000001A                 ENDP
  230 0000001A         
  231 0000001A         Default_Handler
                               PROC
  232 0000001A         ;               /* external interrupts handler */
  233 0000001A                 EXPORT           WWDGT_IRQHandler               
   [WEAK]
  234 0000001A                 EXPORT           LVD_IRQHandler                 
   [WEAK]
  235 0000001A                 EXPORT           TAMPER_STAMP_IRQHandler        
   [WEAK]
  236 0000001A                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  237 0000001A                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  238 0000001A                 EXPORT           RCU_CTC_IRQHandler             
   [WEAK]
  239 0000001A                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  240 0000001A                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  241 0000001A                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  242 0000001A                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  243 0000001A                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  244 0000001A                 EXPORT           DMA0_Channel0_IRQHandler       
   [WEAK]
  245 0000001A                 EXPORT           DMA0_Channel1_IRQHandler       
   [WEAK]
  246 0000001A                 EXPORT           DMA0_Channel2_IRQHandler       
   [WEAK]
  247 0000001A                 EXPORT           DMA0_Channel3_IRQHandler       
   [WEAK]
  248 0000001A                 EXPORT           DMA0_Channel4_IRQHandler       
   [WEAK]
  249 0000001A                 EXPORT           DMA0_Channel5_IRQHandler       
   [WEAK]
  250 0000001A                 EXPORT           DMA0_Channel6_IRQHandler       
   [WEAK]
  251 0000001A                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  252 0000001A                 EXPORT           CAN0_TX_IRQHandler             
   [WEAK]
  253 0000001A                 EXPORT           CAN0_RX0_IRQHandler            
   [WEAK]
  254 0000001A                 EXPORT           CAN0_RX1_IRQHandler            
   [WEAK]
  255 0000001A                 EXPORT           CAN0_EWMC_IRQHandler           
   [WEAK]
  256 0000001A                 EXPORT           EXTI5_9_IRQHandler             
   [WEAK]
  257 0000001A                 EXPORT           TIMER0_BRK_TIMER8_IRQHandler   
   [WEAK]



ARM Macro Assembler    Page 8 


  258 0000001A                 EXPORT           TIMER0_UP_TIMER9_IRQHandler    
   [WEAK]
  259 0000001A                 EXPORT           TIMER0_TRG_CMT_TIMER10_IRQHandl
er [WEAK]
  260 0000001A                 EXPORT           TIMER0_Channel_IRQHandler      
   [WEAK]
  261 0000001A                 EXPORT           TIMER1_IRQHandler              
   [WEAK]
  262 0000001A                 EXPORT           TIMER2_IRQHandler              
   [WEAK]
  263 0000001A                 EXPORT           TIMER3_IRQHandler              
   [WEAK]
  264 0000001A                 EXPORT           I2C0_EV_IRQHandler             
   [WEAK]
  265 0000001A                 EXPORT           I2C0_ER_IRQHandler             
   [WEAK]
  266 0000001A                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  267 0000001A                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  268 0000001A                 EXPORT           SPI0_IRQHandler                
   [WEAK]
  269 0000001A                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  270 0000001A                 EXPORT           USART0_IRQHandler              
   [WEAK]
  271 0000001A                 EXPORT           USART1_IRQHandler              
   [WEAK]
  272 0000001A                 EXPORT           USART2_IRQHandler              
   [WEAK]
  273 0000001A                 EXPORT           EXTI10_15_IRQHandler           
   [WEAK]
  274 0000001A                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  275 0000001A                 EXPORT           USBFS_WKUP_IRQHandler          
   [WEAK]
  276 0000001A                 EXPORT           TIMER7_BRK_TIMER11_IRQHandler  
   [WEAK]
  277 0000001A                 EXPORT           TIMER7_UP_TIMER12_IRQHandler   
   [WEAK]
  278 0000001A                 EXPORT           TIMER7_TRG_CMT_TIMER13_IRQHandl
er [WEAK]
  279 0000001A                 EXPORT           TIMER7_Channel_IRQHandler      
   [WEAK]
  280 0000001A                 EXPORT           DMA0_Channel7_IRQHandler       
   [WEAK]
  281 0000001A                 EXPORT           EXMC_IRQHandler                
   [WEAK]
  282 0000001A                 EXPORT           SDIO_IRQHandler                
   [WEAK]
  283 0000001A                 EXPORT           TIMER4_IRQHandler              
   [WEAK]
  284 0000001A                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  285 0000001A                 EXPORT           UART3_IRQHandler               
   [WEAK]
  286 0000001A                 EXPORT           UART4_IRQHandler               
   [WEAK]
  287 0000001A                 EXPORT           TIMER5_DAC_IRQHandler          



ARM Macro Assembler    Page 9 


   [WEAK]
  288 0000001A                 EXPORT           TIMER6_IRQHandler              
   [WEAK]
  289 0000001A                 EXPORT           DMA1_Channel0_IRQHandler       
   [WEAK]
  290 0000001A                 EXPORT           DMA1_Channel1_IRQHandler       
   [WEAK]
  291 0000001A                 EXPORT           DMA1_Channel2_IRQHandler       
   [WEAK]
  292 0000001A                 EXPORT           DMA1_Channel3_IRQHandler       
   [WEAK]
  293 0000001A                 EXPORT           DMA1_Channel4_IRQHandler       
   [WEAK]
  294 0000001A                 EXPORT           ENET_IRQHandler                
   [WEAK]
  295 0000001A                 EXPORT           ENET_WKUP_IRQHandler           
   [WEAK]
  296 0000001A                 EXPORT           CAN1_TX_IRQHandler             
   [WEAK]
  297 0000001A                 EXPORT           CAN1_RX0_IRQHandler            
   [WEAK]
  298 0000001A                 EXPORT           CAN1_RX1_IRQHandler            
   [WEAK]
  299 0000001A                 EXPORT           CAN1_EWMC_IRQHandler           
   [WEAK]
  300 0000001A                 EXPORT           USBFS_IRQHandler               
   [WEAK]
  301 0000001A                 EXPORT           DMA1_Channel5_IRQHandler       
   [WEAK]
  302 0000001A                 EXPORT           DMA1_Channel6_IRQHandler       
   [WEAK]
  303 0000001A                 EXPORT           DMA1_Channel7_IRQHandler       
   [WEAK]
  304 0000001A                 EXPORT           USART5_IRQHandler              
   [WEAK]
  305 0000001A                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  306 0000001A                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  307 0000001A                 EXPORT           USBHS_EP1_Out_IRQHandler       
   [WEAK]
  308 0000001A                 EXPORT           USBHS_EP1_In_IRQHandler        
   [WEAK]
  309 0000001A                 EXPORT           USBHS_WKUP_IRQHandler          
   [WEAK]
  310 0000001A                 EXPORT           USBHS_IRQHandler               
   [WEAK]
  311 0000001A                 EXPORT           DCI_IRQHandler                 
   [WEAK]
  312 0000001A                 EXPORT           TRNG_IRQHandler                
   [WEAK]
  313 0000001A                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  314 0000001A         
  315 0000001A         ;/* external interrupts handler */
  316 0000001A         WWDGT_IRQHandler
  317 0000001A         LVD_IRQHandler
  318 0000001A         TAMPER_STAMP_IRQHandler
  319 0000001A         RTC_WKUP_IRQHandler



ARM Macro Assembler    Page 10 


  320 0000001A         FMC_IRQHandler
  321 0000001A         RCU_CTC_IRQHandler
  322 0000001A         EXTI0_IRQHandler
  323 0000001A         EXTI1_IRQHandler
  324 0000001A         EXTI2_IRQHandler
  325 0000001A         EXTI3_IRQHandler
  326 0000001A         EXTI4_IRQHandler
  327 0000001A         DMA0_Channel0_IRQHandler
  328 0000001A         DMA0_Channel1_IRQHandler
  329 0000001A         DMA0_Channel2_IRQHandler
  330 0000001A         DMA0_Channel3_IRQHandler
  331 0000001A         DMA0_Channel4_IRQHandler
  332 0000001A         DMA0_Channel5_IRQHandler
  333 0000001A         DMA0_Channel6_IRQHandler
  334 0000001A         ADC_IRQHandler
  335 0000001A         CAN0_TX_IRQHandler
  336 0000001A         CAN0_RX0_IRQHandler
  337 0000001A         CAN0_RX1_IRQHandler
  338 0000001A         CAN0_EWMC_IRQHandler
  339 0000001A         EXTI5_9_IRQHandler
  340 0000001A         TIMER0_BRK_TIMER8_IRQHandler
  341 0000001A         TIMER0_UP_TIMER9_IRQHandler
  342 0000001A         TIMER0_TRG_CMT_TIMER10_IRQHandler
  343 0000001A         TIMER0_Channel_IRQHandler
  344 0000001A         TIMER1_IRQHandler
  345 0000001A         TIMER2_IRQHandler
  346 0000001A         TIMER3_IRQHandler
  347 0000001A         I2C0_EV_IRQHandler
  348 0000001A         I2C0_ER_IRQHandler
  349 0000001A         I2C1_EV_IRQHandler
  350 0000001A         I2C1_ER_IRQHandler
  351 0000001A         SPI0_IRQHandler
  352 0000001A         SPI1_IRQHandler
  353 0000001A         USART0_IRQHandler
  354 0000001A         USART1_IRQHandler
  355 0000001A         USART2_IRQHandler
  356 0000001A         EXTI10_15_IRQHandler
  357 0000001A         RTC_Alarm_IRQHandler
  358 0000001A         USBFS_WKUP_IRQHandler
  359 0000001A         TIMER7_BRK_TIMER11_IRQHandler
  360 0000001A         TIMER7_UP_TIMER12_IRQHandler
  361 0000001A         TIMER7_TRG_CMT_TIMER13_IRQHandler
  362 0000001A         TIMER7_Channel_IRQHandler
  363 0000001A         DMA0_Channel7_IRQHandler
  364 0000001A         EXMC_IRQHandler
  365 0000001A         SDIO_IRQHandler
  366 0000001A         TIMER4_IRQHandler
  367 0000001A         SPI2_IRQHandler
  368 0000001A         UART3_IRQHandler
  369 0000001A         UART4_IRQHandler
  370 0000001A         TIMER5_DAC_IRQHandler
  371 0000001A         TIMER6_IRQHandler
  372 0000001A         DMA1_Channel0_IRQHandler
  373 0000001A         DMA1_Channel1_IRQHandler
  374 0000001A         DMA1_Channel2_IRQHandler
  375 0000001A         DMA1_Channel3_IRQHandler
  376 0000001A         DMA1_Channel4_IRQHandler
  377 0000001A         ENET_IRQHandler
  378 0000001A         ENET_WKUP_IRQHandler



ARM Macro Assembler    Page 11 


  379 0000001A         CAN1_TX_IRQHandler
  380 0000001A         CAN1_RX0_IRQHandler
  381 0000001A         CAN1_RX1_IRQHandler
  382 0000001A         CAN1_EWMC_IRQHandler
  383 0000001A         USBFS_IRQHandler
  384 0000001A         DMA1_Channel5_IRQHandler
  385 0000001A         DMA1_Channel6_IRQHandler
  386 0000001A         DMA1_Channel7_IRQHandler
  387 0000001A         USART5_IRQHandler
  388 0000001A         I2C2_EV_IRQHandler
  389 0000001A         I2C2_ER_IRQHandler
  390 0000001A         USBHS_EP1_Out_IRQHandler
  391 0000001A         USBHS_EP1_In_IRQHandler
  392 0000001A         USBHS_WKUP_IRQHandler
  393 0000001A         USBHS_IRQHandler
  394 0000001A         DCI_IRQHandler
  395 0000001A         TRNG_IRQHandler
  396 0000001A         FPU_IRQHandler
  397 0000001A         
  398 0000001A E7FE            B                .
  399 0000001C                 ENDP
  400 0000001C         
  401 0000001C                 ALIGN
  402 0000001C         
  403 0000001C         ; user Initial Stack & Heap
  404 0000001C         
  405 0000001C                 IF               :DEF:__MICROLIB
  412 0000001C         
  413 0000001C                 IMPORT           __use_two_region_memory
  414 0000001C                 EXPORT           __user_initial_stackheap
  415 0000001C         
  416 0000001C         __user_initial_stackheap
                               PROC
  417 0000001C 4804            LDR              R0, =  Heap_Mem
  418 0000001E 4905            LDR              R1, =(Stack_Mem + Stack_Size)
  419 00000020 4A05            LDR              R2, = (Heap_Mem +  Heap_Size)
  420 00000022 4B06            LDR              R3, = Stack_Mem
  421 00000024 4770            BX               LR
  422 00000026                 ENDP
  423 00000026         
  424 00000026 00 00           ALIGN
  425 00000028         
  426 00000028                 ENDIF
  427 00000028         
  428 00000028                 END
              00000000 
              00000000 
              00000000 
              00000400 
              00000400 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp.sp --apcs=
interwork --depend=.\objects\startup_gd32f407.d -o.\objects\startup_gd32f407.o 
-ID:\Keil_v5\ARM\PACK\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include --prede
fine="__UVISION_VERSION SETA 536" --predefine="GD32F407 SETA 1" --list=.\listin
gs\startup_gd32f407.lst ..\Startup\startup_gd32f407.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 43 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 44 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 418 in file ..\Startup\startup_gd32f407.s
      At line 420 in file ..\Startup\startup_gd32f407.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 45 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 68 in file ..\Startup\startup_gd32f407.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 54 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 56 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 417 in file ..\Startup\startup_gd32f407.s
      At line 419 in file ..\Startup\startup_gd32f407.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 55 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000400

Symbol: __heap_limit
   Definitions
      At line 57 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 63 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 68 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 64 in file ..\Startup\startup_gd32f407.s
      At line 171 in file ..\Startup\startup_gd32f407.s

__Vectors_End 00000188

Symbol: __Vectors_End
   Definitions
      At line 169 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 65 in file ..\Startup\startup_gd32f407.s
      At line 171 in file ..\Startup\startup_gd32f407.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 173 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: .text unused
ADC_IRQHandler 0000001A

Symbol: ADC_IRQHandler
   Definitions
      At line 334 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 104 in file ..\Startup\startup_gd32f407.s
      At line 251 in file ..\Startup\startup_gd32f407.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 202 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 73 in file ..\Startup\startup_gd32f407.s
      At line 203 in file ..\Startup\startup_gd32f407.s

CAN0_EWMC_IRQHandler 0000001A

Symbol: CAN0_EWMC_IRQHandler
   Definitions
      At line 338 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 108 in file ..\Startup\startup_gd32f407.s
      At line 255 in file ..\Startup\startup_gd32f407.s

CAN0_RX0_IRQHandler 0000001A

Symbol: CAN0_RX0_IRQHandler
   Definitions
      At line 336 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 106 in file ..\Startup\startup_gd32f407.s
      At line 253 in file ..\Startup\startup_gd32f407.s

CAN0_RX1_IRQHandler 0000001A

Symbol: CAN0_RX1_IRQHandler
   Definitions
      At line 337 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 107 in file ..\Startup\startup_gd32f407.s
      At line 254 in file ..\Startup\startup_gd32f407.s

CAN0_TX_IRQHandler 0000001A

Symbol: CAN0_TX_IRQHandler
   Definitions
      At line 335 in file ..\Startup\startup_gd32f407.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 105 in file ..\Startup\startup_gd32f407.s
      At line 252 in file ..\Startup\startup_gd32f407.s

CAN1_EWMC_IRQHandler 0000001A

Symbol: CAN1_EWMC_IRQHandler
   Definitions
      At line 382 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 152 in file ..\Startup\startup_gd32f407.s
      At line 299 in file ..\Startup\startup_gd32f407.s

CAN1_RX0_IRQHandler 0000001A

Symbol: CAN1_RX0_IRQHandler
   Definitions
      At line 380 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 150 in file ..\Startup\startup_gd32f407.s
      At line 297 in file ..\Startup\startup_gd32f407.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 381 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 151 in file ..\Startup\startup_gd32f407.s
      At line 298 in file ..\Startup\startup_gd32f407.s

CAN1_TX_IRQHandler 0000001A

Symbol: CAN1_TX_IRQHandler
   Definitions
      At line 379 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 149 in file ..\Startup\startup_gd32f407.s
      At line 296 in file ..\Startup\startup_gd32f407.s

DCI_IRQHandler 0000001A

Symbol: DCI_IRQHandler
   Definitions
      At line 394 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 164 in file ..\Startup\startup_gd32f407.s
      At line 311 in file ..\Startup\startup_gd32f407.s

DMA0_Channel0_IRQHandler 0000001A

Symbol: DMA0_Channel0_IRQHandler
   Definitions
      At line 327 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 97 in file ..\Startup\startup_gd32f407.s
      At line 244 in file ..\Startup\startup_gd32f407.s

DMA0_Channel1_IRQHandler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: DMA0_Channel1_IRQHandler
   Definitions
      At line 328 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 98 in file ..\Startup\startup_gd32f407.s
      At line 245 in file ..\Startup\startup_gd32f407.s

DMA0_Channel2_IRQHandler 0000001A

Symbol: DMA0_Channel2_IRQHandler
   Definitions
      At line 329 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 99 in file ..\Startup\startup_gd32f407.s
      At line 246 in file ..\Startup\startup_gd32f407.s

DMA0_Channel3_IRQHandler 0000001A

Symbol: DMA0_Channel3_IRQHandler
   Definitions
      At line 330 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 100 in file ..\Startup\startup_gd32f407.s
      At line 247 in file ..\Startup\startup_gd32f407.s

DMA0_Channel4_IRQHandler 0000001A

Symbol: DMA0_Channel4_IRQHandler
   Definitions
      At line 331 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 101 in file ..\Startup\startup_gd32f407.s
      At line 248 in file ..\Startup\startup_gd32f407.s

DMA0_Channel5_IRQHandler 0000001A

Symbol: DMA0_Channel5_IRQHandler
   Definitions
      At line 332 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 102 in file ..\Startup\startup_gd32f407.s
      At line 249 in file ..\Startup\startup_gd32f407.s

DMA0_Channel6_IRQHandler 0000001A

Symbol: DMA0_Channel6_IRQHandler
   Definitions
      At line 333 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 103 in file ..\Startup\startup_gd32f407.s
      At line 250 in file ..\Startup\startup_gd32f407.s

DMA0_Channel7_IRQHandler 0000001A

Symbol: DMA0_Channel7_IRQHandler
   Definitions
      At line 363 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 133 in file ..\Startup\startup_gd32f407.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 280 in file ..\Startup\startup_gd32f407.s

DMA1_Channel0_IRQHandler 0000001A

Symbol: DMA1_Channel0_IRQHandler
   Definitions
      At line 372 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 142 in file ..\Startup\startup_gd32f407.s
      At line 289 in file ..\Startup\startup_gd32f407.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 373 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 143 in file ..\Startup\startup_gd32f407.s
      At line 290 in file ..\Startup\startup_gd32f407.s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 374 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 144 in file ..\Startup\startup_gd32f407.s
      At line 291 in file ..\Startup\startup_gd32f407.s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 375 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 145 in file ..\Startup\startup_gd32f407.s
      At line 292 in file ..\Startup\startup_gd32f407.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 376 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 146 in file ..\Startup\startup_gd32f407.s
      At line 293 in file ..\Startup\startup_gd32f407.s

DMA1_Channel5_IRQHandler 0000001A

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 384 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 154 in file ..\Startup\startup_gd32f407.s
      At line 301 in file ..\Startup\startup_gd32f407.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 385 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 155 in file ..\Startup\startup_gd32f407.s
      At line 302 in file ..\Startup\startup_gd32f407.s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 386 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 156 in file ..\Startup\startup_gd32f407.s
      At line 303 in file ..\Startup\startup_gd32f407.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 216 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 80 in file ..\Startup\startup_gd32f407.s
      At line 217 in file ..\Startup\startup_gd32f407.s

Default_Handler 0000001A

Symbol: Default_Handler
   Definitions
      At line 231 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: Default_Handler unused
ENET_IRQHandler 0000001A

Symbol: ENET_IRQHandler
   Definitions
      At line 377 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 147 in file ..\Startup\startup_gd32f407.s
      At line 294 in file ..\Startup\startup_gd32f407.s

ENET_WKUP_IRQHandler 0000001A

Symbol: ENET_WKUP_IRQHandler
   Definitions
      At line 378 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 148 in file ..\Startup\startup_gd32f407.s
      At line 295 in file ..\Startup\startup_gd32f407.s

EXMC_IRQHandler 0000001A

Symbol: EXMC_IRQHandler
   Definitions
      At line 364 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 134 in file ..\Startup\startup_gd32f407.s
      At line 281 in file ..\Startup\startup_gd32f407.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 322 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 92 in file ..\Startup\startup_gd32f407.s
      At line 239 in file ..\Startup\startup_gd32f407.s

EXTI10_15_IRQHandler 0000001A

Symbol: EXTI10_15_IRQHandler
   Definitions
      At line 356 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 126 in file ..\Startup\startup_gd32f407.s
      At line 273 in file ..\Startup\startup_gd32f407.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 323 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 93 in file ..\Startup\startup_gd32f407.s
      At line 240 in file ..\Startup\startup_gd32f407.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 324 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 94 in file ..\Startup\startup_gd32f407.s
      At line 241 in file ..\Startup\startup_gd32f407.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 325 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 95 in file ..\Startup\startup_gd32f407.s
      At line 242 in file ..\Startup\startup_gd32f407.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 326 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 96 in file ..\Startup\startup_gd32f407.s
      At line 243 in file ..\Startup\startup_gd32f407.s

EXTI5_9_IRQHandler 0000001A

Symbol: EXTI5_9_IRQHandler
   Definitions
      At line 339 in file ..\Startup\startup_gd32f407.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 109 in file ..\Startup\startup_gd32f407.s
      At line 256 in file ..\Startup\startup_gd32f407.s

FMC_IRQHandler 0000001A

Symbol: FMC_IRQHandler
   Definitions
      At line 320 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 90 in file ..\Startup\startup_gd32f407.s
      At line 237 in file ..\Startup\startup_gd32f407.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 396 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 167 in file ..\Startup\startup_gd32f407.s
      At line 313 in file ..\Startup\startup_gd32f407.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 192 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 71 in file ..\Startup\startup_gd32f407.s
      At line 193 in file ..\Startup\startup_gd32f407.s

I2C0_ER_IRQHandler 0000001A

Symbol: I2C0_ER_IRQHandler
   Definitions
      At line 348 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 118 in file ..\Startup\startup_gd32f407.s
      At line 265 in file ..\Startup\startup_gd32f407.s

I2C0_EV_IRQHandler 0000001A

Symbol: I2C0_EV_IRQHandler
   Definitions
      At line 347 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 117 in file ..\Startup\startup_gd32f407.s
      At line 264 in file ..\Startup\startup_gd32f407.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 350 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 120 in file ..\Startup\startup_gd32f407.s
      At line 267 in file ..\Startup\startup_gd32f407.s

I2C1_EV_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 349 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 119 in file ..\Startup\startup_gd32f407.s
      At line 266 in file ..\Startup\startup_gd32f407.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 389 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 159 in file ..\Startup\startup_gd32f407.s
      At line 306 in file ..\Startup\startup_gd32f407.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 388 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 158 in file ..\Startup\startup_gd32f407.s
      At line 305 in file ..\Startup\startup_gd32f407.s

LVD_IRQHandler 0000001A

Symbol: LVD_IRQHandler
   Definitions
      At line 317 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 87 in file ..\Startup\startup_gd32f407.s
      At line 234 in file ..\Startup\startup_gd32f407.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 197 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 72 in file ..\Startup\startup_gd32f407.s
      At line 198 in file ..\Startup\startup_gd32f407.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 187 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 70 in file ..\Startup\startup_gd32f407.s
      At line 188 in file ..\Startup\startup_gd32f407.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 221 in file ..\Startup\startup_gd32f407.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 82 in file ..\Startup\startup_gd32f407.s
      At line 222 in file ..\Startup\startup_gd32f407.s

RCU_CTC_IRQHandler 0000001A

Symbol: RCU_CTC_IRQHandler
   Definitions
      At line 321 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 91 in file ..\Startup\startup_gd32f407.s
      At line 238 in file ..\Startup\startup_gd32f407.s

RTC_Alarm_IRQHandler 0000001A

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 357 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 127 in file ..\Startup\startup_gd32f407.s
      At line 274 in file ..\Startup\startup_gd32f407.s

RTC_WKUP_IRQHandler 0000001A

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 319 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 89 in file ..\Startup\startup_gd32f407.s
      At line 236 in file ..\Startup\startup_gd32f407.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 176 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 69 in file ..\Startup\startup_gd32f407.s
      At line 177 in file ..\Startup\startup_gd32f407.s

SDIO_IRQHandler 0000001A

Symbol: SDIO_IRQHandler
   Definitions
      At line 365 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 135 in file ..\Startup\startup_gd32f407.s
      At line 282 in file ..\Startup\startup_gd32f407.s

SPI0_IRQHandler 0000001A

Symbol: SPI0_IRQHandler
   Definitions
      At line 351 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 121 in file ..\Startup\startup_gd32f407.s
      At line 268 in file ..\Startup\startup_gd32f407.s

SPI1_IRQHandler 0000001A




ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols

Symbol: SPI1_IRQHandler
   Definitions
      At line 352 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 122 in file ..\Startup\startup_gd32f407.s
      At line 269 in file ..\Startup\startup_gd32f407.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 367 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 137 in file ..\Startup\startup_gd32f407.s
      At line 284 in file ..\Startup\startup_gd32f407.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 211 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 79 in file ..\Startup\startup_gd32f407.s
      At line 212 in file ..\Startup\startup_gd32f407.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 226 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 83 in file ..\Startup\startup_gd32f407.s
      At line 227 in file ..\Startup\startup_gd32f407.s

TAMPER_STAMP_IRQHandler 0000001A

Symbol: TAMPER_STAMP_IRQHandler
   Definitions
      At line 318 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 88 in file ..\Startup\startup_gd32f407.s
      At line 235 in file ..\Startup\startup_gd32f407.s

TIMER0_BRK_TIMER8_IRQHandler 0000001A

Symbol: TIMER0_BRK_TIMER8_IRQHandler
   Definitions
      At line 340 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 110 in file ..\Startup\startup_gd32f407.s
      At line 257 in file ..\Startup\startup_gd32f407.s

TIMER0_Channel_IRQHandler 0000001A

Symbol: TIMER0_Channel_IRQHandler
   Definitions
      At line 343 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 113 in file ..\Startup\startup_gd32f407.s



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 260 in file ..\Startup\startup_gd32f407.s

TIMER0_TRG_CMT_TIMER10_IRQHandler 0000001A

Symbol: TIMER0_TRG_CMT_TIMER10_IRQHandler
   Definitions
      At line 342 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 112 in file ..\Startup\startup_gd32f407.s
      At line 259 in file ..\Startup\startup_gd32f407.s

TIMER0_UP_TIMER9_IRQHandler 0000001A

Symbol: TIMER0_UP_TIMER9_IRQHandler
   Definitions
      At line 341 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 111 in file ..\Startup\startup_gd32f407.s
      At line 258 in file ..\Startup\startup_gd32f407.s

TIMER1_IRQHandler 0000001A

Symbol: TIMER1_IRQHandler
   Definitions
      At line 344 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 114 in file ..\Startup\startup_gd32f407.s
      At line 261 in file ..\Startup\startup_gd32f407.s

TIMER2_IRQHandler 0000001A

Symbol: TIMER2_IRQHandler
   Definitions
      At line 345 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 115 in file ..\Startup\startup_gd32f407.s
      At line 262 in file ..\Startup\startup_gd32f407.s

TIMER3_IRQHandler 0000001A

Symbol: TIMER3_IRQHandler
   Definitions
      At line 346 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 116 in file ..\Startup\startup_gd32f407.s
      At line 263 in file ..\Startup\startup_gd32f407.s

TIMER4_IRQHandler 0000001A

Symbol: TIMER4_IRQHandler
   Definitions
      At line 366 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 136 in file ..\Startup\startup_gd32f407.s
      At line 283 in file ..\Startup\startup_gd32f407.s

TIMER5_DAC_IRQHandler 0000001A

Symbol: TIMER5_DAC_IRQHandler



ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 370 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 140 in file ..\Startup\startup_gd32f407.s
      At line 287 in file ..\Startup\startup_gd32f407.s

TIMER6_IRQHandler 0000001A

Symbol: TIMER6_IRQHandler
   Definitions
      At line 371 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 141 in file ..\Startup\startup_gd32f407.s
      At line 288 in file ..\Startup\startup_gd32f407.s

TIMER7_BRK_TIMER11_IRQHandler 0000001A

Symbol: TIMER7_BRK_TIMER11_IRQHandler
   Definitions
      At line 359 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 129 in file ..\Startup\startup_gd32f407.s
      At line 276 in file ..\Startup\startup_gd32f407.s

TIMER7_Channel_IRQHandler 0000001A

Symbol: TIMER7_Channel_IRQHandler
   Definitions
      At line 362 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 132 in file ..\Startup\startup_gd32f407.s
      At line 279 in file ..\Startup\startup_gd32f407.s

TIMER7_TRG_CMT_TIMER13_IRQHandler 0000001A

Symbol: TIMER7_TRG_CMT_TIMER13_IRQHandler
   Definitions
      At line 361 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 131 in file ..\Startup\startup_gd32f407.s
      At line 278 in file ..\Startup\startup_gd32f407.s

TIMER7_UP_TIMER12_IRQHandler 0000001A

Symbol: TIMER7_UP_TIMER12_IRQHandler
   Definitions
      At line 360 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 130 in file ..\Startup\startup_gd32f407.s
      At line 277 in file ..\Startup\startup_gd32f407.s

TRNG_IRQHandler 0000001A

Symbol: TRNG_IRQHandler
   Definitions
      At line 395 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 166 in file ..\Startup\startup_gd32f407.s
      At line 312 in file ..\Startup\startup_gd32f407.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols


UART3_IRQHandler 0000001A

Symbol: UART3_IRQHandler
   Definitions
      At line 368 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 138 in file ..\Startup\startup_gd32f407.s
      At line 285 in file ..\Startup\startup_gd32f407.s

UART4_IRQHandler 0000001A

Symbol: UART4_IRQHandler
   Definitions
      At line 369 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 139 in file ..\Startup\startup_gd32f407.s
      At line 286 in file ..\Startup\startup_gd32f407.s

USART0_IRQHandler 0000001A

Symbol: USART0_IRQHandler
   Definitions
      At line 353 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 123 in file ..\Startup\startup_gd32f407.s
      At line 270 in file ..\Startup\startup_gd32f407.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 354 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 124 in file ..\Startup\startup_gd32f407.s
      At line 271 in file ..\Startup\startup_gd32f407.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 355 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 125 in file ..\Startup\startup_gd32f407.s
      At line 272 in file ..\Startup\startup_gd32f407.s

USART5_IRQHandler 0000001A

Symbol: USART5_IRQHandler
   Definitions
      At line 387 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 157 in file ..\Startup\startup_gd32f407.s
      At line 304 in file ..\Startup\startup_gd32f407.s

USBFS_IRQHandler 0000001A

Symbol: USBFS_IRQHandler
   Definitions



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

      At line 383 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 153 in file ..\Startup\startup_gd32f407.s
      At line 300 in file ..\Startup\startup_gd32f407.s

USBFS_WKUP_IRQHandler 0000001A

Symbol: USBFS_WKUP_IRQHandler
   Definitions
      At line 358 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 128 in file ..\Startup\startup_gd32f407.s
      At line 275 in file ..\Startup\startup_gd32f407.s

USBHS_EP1_In_IRQHandler 0000001A

Symbol: USBHS_EP1_In_IRQHandler
   Definitions
      At line 391 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 161 in file ..\Startup\startup_gd32f407.s
      At line 308 in file ..\Startup\startup_gd32f407.s

USBHS_EP1_Out_IRQHandler 0000001A

Symbol: USBHS_EP1_Out_IRQHandler
   Definitions
      At line 390 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 160 in file ..\Startup\startup_gd32f407.s
      At line 307 in file ..\Startup\startup_gd32f407.s

USBHS_IRQHandler 0000001A

Symbol: USBHS_IRQHandler
   Definitions
      At line 393 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 163 in file ..\Startup\startup_gd32f407.s
      At line 310 in file ..\Startup\startup_gd32f407.s

USBHS_WKUP_IRQHandler 0000001A

Symbol: USBHS_WKUP_IRQHandler
   Definitions
      At line 392 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 162 in file ..\Startup\startup_gd32f407.s
      At line 309 in file ..\Startup\startup_gd32f407.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 207 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 74 in file ..\Startup\startup_gd32f407.s
      At line 208 in file ..\Startup\startup_gd32f407.s




ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols

WWDGT_IRQHandler 0000001A

Symbol: WWDGT_IRQHandler
   Definitions
      At line 316 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 86 in file ..\Startup\startup_gd32f407.s
      At line 233 in file ..\Startup\startup_gd32f407.s

__user_initial_stackheap 0000001C

Symbol: __user_initial_stackheap
   Definitions
      At line 416 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 414 in file ..\Startup\startup_gd32f407.s
Comment: __user_initial_stackheap used once
94 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000400

Symbol: Heap_Size
   Definitions
      At line 52 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 56 in file ..\Startup\startup_gd32f407.s
      At line 419 in file ..\Startup\startup_gd32f407.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 41 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 44 in file ..\Startup\startup_gd32f407.s
      At line 418 in file ..\Startup\startup_gd32f407.s

__Vectors_Size 00000188

Symbol: __Vectors_Size
   Definitions
      At line 171 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 66 in file ..\Startup\startup_gd32f407.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 178 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 180 in file ..\Startup\startup_gd32f407.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 179 in file ..\Startup\startup_gd32f407.s
   Uses
      At line 182 in file ..\Startup\startup_gd32f407.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 413 in file ..\Startup\startup_gd32f407.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
447 symbols in table
