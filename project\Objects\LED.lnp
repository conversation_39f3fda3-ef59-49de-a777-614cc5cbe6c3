--cpu=Cortex-M4.fp.sp
".\objects\gd32f4xx_it.o"
".\objects\main.o"
".\objects\systick.o"
".\objects\led.o"
".\objects\key.o"
".\objects\lan8720.o"
".\objects\lcd.o"
".\objects\usart0.o"
".\objects\implement.o"
".\objects\timer.o"
".\objects\system_gd32f4xx.o"
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
".\objects\startup_gd32f450_470.o"
".\objects\lwip_comm.o"
".\objects\etharp.o"
".\objects\ethernetif.o"
".\objects\autoip.o"
".\objects\icmp.o"
".\objects\igmp.o"
".\objects\inet.o"
".\objects\inet_chksum.o"
".\objects\ip.o"
".\objects\ip_addr.o"
".\objects\ip_frag.o"
".\objects\def.o"
".\objects\dhcp.o"
".\objects\dns.o"
".\objects\init.o"
".\objects\mem.o"
".\objects\memp.o"
".\objects\netif.o"
".\objects\pbuf.o"
".\objects\raw.o"
".\objects\stats.o"
".\objects\tcp.o"
".\objects\tcp_in.o"
".\objects\tcp_out.o"
".\objects\timers.o"
".\objects\udp.o"
".\objects\lwip_sys.o"
".\objects\sys_arch.o"
".\objects\api_lib.o"
".\objects\api_msg.o"
".\objects\err.o"
".\objects\netbuf.o"
".\objects\netdb.o"
".\objects\netifapi.o"
".\objects\sockets.o"
".\objects\tcpip.o"
".\objects\malloc.o"
".\objects\croutine.o"
".\objects\event_groups.o"
".\objects\list.o"
".\objects\queue.o"
".\objects\tasks.o"
".\objects\timers_1.o"
".\objects\heap_4.o"
".\objects\port.o"
--library_type=microlib --strict --scatter ".\Objects\LED.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\LED.map" -o .\Objects\LED.axf