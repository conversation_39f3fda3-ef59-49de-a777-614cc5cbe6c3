Dependencies for Project 'LWIP', Target 'LWIP': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\User\gd32f4xx_it.c)(0x663F8372)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\User\gd32f4xx_it.h)(0x65A7AAAA)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
F (..\User\main.c)(0x66605274)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\User\systick.c)(0x66050E56)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
F (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)()
F (..\HardWare\LED\LED.c)(0x664425B0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\led.o --omf_browse .\objects\led.crf --depend .\objects\led.d)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\HardWare\KEY\KEY.c)(0x66605158)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\key.o --omf_browse .\objects\key.crf --depend .\objects\key.d)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\HardWare\LAN8720\lan8720.c)(0x6660512C)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\lan8720.o --omf_browse .\objects\lan8720.crf --depend .\objects\lan8720.d)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\HardWare\LCD\LCD.c)(0x66444CF2)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\lcd.o --omf_browse .\objects\lcd.crf --depend .\objects\lcd.d)
I (..\HardWare\LCD\LCD.h)(0x66444CF2)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\HardWare\LCD\font.h)(0x62582018)
F (..\Protocol\USART0\USART0.c)(0x66605274)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\usart0.o --omf_browse .\objects\usart0.crf --depend .\objects\usart0.d)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\Implement\Implement.c)(0x6894110F)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\implement.o --omf_browse .\objects\implement.crf --depend .\objects\implement.d)
I (..\Implement\Implement.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
F (..\HeaderFiles\HeaderFiles.h)(0x68941062)()
F (..\System\TIMER\TIMER.c)(0x66605274)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timer.o --omf_browse .\objects\timer.crf --depend .\objects\timer.d)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\CMSIS\system_gd32f4xx.c)(0x6623E0A0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x6382D6DC)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x6229735A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\Startup\startup_gd32f450_470.s)(0x65A7AAB6)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 533" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\LWIP\lwip_app\lwip_comm\lwip_comm.c)(0x66605274)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\lwip_comm.o --omf_browse .\objects\lwip_comm.crf --depend .\objects\lwip_comm.d)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/init.h)(0x50CF0E52)
I (..\LWIP\lwip-1.4.1\src\include\lwip/timers.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcpip.h)(0x50CEFB60)
F (..\LWIP\lwip-1.4.1\src\netif\etharp.c)(0x54463C9A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\etharp.o --omf_browse .\objects\etharp.crf --depend .\objects\etharp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/autoip.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\netif\ethernetif.c)(0x66605274)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ethernetif.o --omf_browse .\objects\ethernetif.crf --depend .\objects\ethernetif.d)
I (..\LWIP\lwip-1.4.1\src\include\netif/ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\autoip.c)(0x50CEF618)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\autoip.o --omf_browse .\objects\autoip.crf --depend .\objects\autoip.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\icmp.c)(0x54451C6A)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\icmp.o --omf_browse .\objects\icmp.crf --depend .\objects\icmp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\igmp.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\igmp.o --omf_browse .\objects\igmp.crf --depend .\objects\igmp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\inet.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\inet.o --omf_browse .\objects\inet.crf --depend .\objects\inet.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\inet_chksum.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\inet_chksum.o --omf_browse .\objects\inet_chksum.crf --depend .\objects\inet_chksum.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\ip.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ip.o --omf_browse .\objects\ip.crf --depend .\objects\ip.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/igmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/raw.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/autoip.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\arch/perf.h)(0x5444DF96)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\ip_addr.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ip_addr.o --omf_browse .\objects\ip_addr.crf --depend .\objects\ip_addr.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
F (..\LWIP\lwip-1.4.1\src\core\ipv4\ip_frag.c)(0x50CEF618)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\ip_frag.o --omf_browse .\objects\ip_frag.crf --depend .\objects\ip_frag.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\def.c)(0x50CEF618)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\def.o --omf_browse .\objects\def.crf --depend .\objects\def.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
F (..\LWIP\lwip-1.4.1\src\core\dhcp.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\dhcp.o --omf_browse .\objects\dhcp.crf --depend .\objects\dhcp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/autoip.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dns.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\dns.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\dns.o --omf_browse .\objects\dns.crf --depend .\objects\dns.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\init.c)(0x5444EA22)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\init.o --omf_browse .\objects\init.crf --depend .\objects\init.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/init.h)(0x50CF0E52)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/sockets.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/raw.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp_msg.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp_structs.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/autoip.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/igmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dns.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/timers.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/api.h)(0x50CEFB60)
F (..\LWIP\lwip-1.4.1\src\core\mem.c)(0x54451A10)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\mem.o --omf_browse .\objects\mem.crf --depend .\objects\mem.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\memp.c)(0x5445197E)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\memp.o --omf_browse .\objects\memp.crf --depend .\objects\memp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/raw.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/igmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/api.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/api_msg.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcpip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/timers.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp_structs.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp_msg.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dns.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\netif/ppp_oe.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\netif.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\netif.o --omf_browse .\objects\netif.crf --depend .\objects\netif.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/igmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
F (..\LWIP\lwip-1.4.1\src\core\pbuf.c)(0x55A8CA66)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\pbuf.o --omf_browse .\objects\pbuf.crf --depend .\objects\pbuf.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
I (..\LWIP\arch/perf.h)(0x5444DF96)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\raw.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\raw.o --omf_browse .\objects\raw.crf --depend .\objects\raw.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/raw.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\arch/perf.h)(0x5444DF96)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\stats.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\stats.o --omf_browse .\objects\stats.crf --depend .\objects\stats.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\tcp.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tcp.o --omf_browse .\objects\tcp.crf --depend .\objects\tcp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\tcp_in.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tcp_in.o --omf_browse .\objects\tcp_in.crf --depend .\objects\tcp_in.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\arch/perf.h)(0x5444DF96)
F (..\LWIP\lwip-1.4.1\src\core\tcp_out.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tcp_out.o --omf_browse .\objects\tcp_out.crf --depend .\objects\tcp_out.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\timers.c)(0x5444EA22)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timers.o --omf_browse .\objects\timers.crf --depend .\objects\timers.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/timers.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp_impl.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/tcpip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_frag.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\netif/etharp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/autoip.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/igmp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dns.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
F (..\LWIP\lwip-1.4.1\src\core\udp.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\udp.o --omf_browse .\objects\udp.crf --depend .\objects\udp.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/udp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/memp_std.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/inet_chksum.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/icmp.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/stats.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/snmp.h)(0x50CEF618)
I (..\LWIP\arch/perf.h)(0x5444DF96)
I (..\LWIP\lwip-1.4.1\src\include\lwip/dhcp.h)(0x50CEF618)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\core\lwip_sys.c)(0x5444EA22)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\lwip_sys.o --omf_browse .\objects\lwip_sys.crf --depend .\objects\lwip_sys.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
F (..\LWIP\arch\sys_arch.c)(0x5416AF38)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sys_arch.o --omf_browse .\objects\sys_arch.crf --depend .\objects\sys_arch.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/lwip_sys.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/mem.h)(0x50CEF618)
I (..\System\TIMER\timer.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\User\systick.h)(0x66050E9C)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
F (..\LWIP\lwip-1.4.1\src\api\api_lib.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\api_lib.o --omf_browse .\objects\api_lib.crf --depend .\objects\api_lib.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\api_msg.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\api_msg.o --omf_browse .\objects\api_msg.crf --depend .\objects\api_msg.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\err.c)(0x50CEF618)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\err.o --omf_browse .\objects\err.crf --depend .\objects\err.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\netbuf.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\netbuf.o --omf_browse .\objects\netbuf.crf --depend .\objects\netbuf.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\netdb.c)(0x50CEF618)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\netdb.o --omf_browse .\objects\netdb.crf --depend .\objects\netdb.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netdb.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\netifapi.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\netifapi.o --omf_browse .\objects\netifapi.crf --depend .\objects\netifapi.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\sockets.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\sockets.o --omf_browse .\objects\sockets.crf --depend .\objects\sockets.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\LWIP\lwip-1.4.1\src\api\tcpip.c)(0x50CEFB60)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tcpip.o --omf_browse .\objects\tcpip.crf --depend .\objects\tcpip.d)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
F (..\MALLOC\malloc.c)(0x636DB3A8)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\malloc.o --omf_browse .\objects\malloc.crf --depend .\objects\malloc.d)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
F (..\FreeRTOS\src\croutine.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\croutine.o --omf_browse .\objects\croutine.crf --depend .\objects\croutine.d)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
I (..\FreeRTOS\include\croutine.h)(0x61C570D0)
F (..\FreeRTOS\src\event_groups.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\event_groups.o --omf_browse .\objects\event_groups.crf --depend .\objects\event_groups.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
I (..\FreeRTOS\include\timers.h)(0x61C570D0)
I (..\FreeRTOS\include\event_groups.h)(0x61C570D0)
F (..\FreeRTOS\src\list.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\list.o --omf_browse .\objects\list.crf --depend .\objects\list.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
F (..\FreeRTOS\src\queue.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\queue.o --omf_browse .\objects\queue.crf --depend .\objects\queue.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
I (..\FreeRTOS\include\queue.h)(0x61C570D0)
F (..\FreeRTOS\src\tasks.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\tasks.o --omf_browse .\objects\tasks.crf --depend .\objects\tasks.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\string.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
I (..\FreeRTOS\include\timers.h)(0x61C570D0)
I (..\FreeRTOS\include\StackMacros.h)(0x61C570D0)
F (..\FreeRTOS\src\timers.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\timers_1.o --omf_browse .\objects\timers_1.crf --depend .\objects\timers_1.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
I (..\FreeRTOS\include\queue.h)(0x61C570D0)
I (..\FreeRTOS\include\timers.h)(0x61C570D0)
F (..\FreeRTOS\port\MemMang\heap_4.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\heap_4.o --omf_browse .\objects\heap_4.crf --depend .\objects\heap_4.d)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
F (..\FreeRTOS\port\RVDS\ARM_CM4F\port.c)(0x61C570D0)(-c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O2 --apcs=interwork -I ..\CMSIS -I ..\Startup -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\User -I ..\HeaderFiles -I ..\Implement -I ..\HardWare\LED -I ..\HardWare\KEY -I ..\HardWare\LAN8720 -I ..\System\TIMER -I ..\Protocol\USART0 -I ..\HardWare\SRAM -I ..\MALLOC -I ..\HardWare\LCD -I ..\LWIP -I ..\LWIP\arch -I ..\LWIP\lwip-1.4.1\src\include -I ..\LWIP\lwip-1.4.1\src\include\ipv4 -I ..\LWIP\lwip-1.4.1\src\include\netif -I ..\LWIP\lwip_app\lwip_comm -I ..\FreeRTOS -I ..\FreeRTOS\include -I ..\FreeRTOS\port\RVDS\ARM_CM4F

-IE:\MDKPacks\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-IE:/MDK533\ARM\CMSIS\Include

-D__UVISION_VERSION="533" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470

-o .\objects\port.o --omf_browse .\objects\port.crf --depend .\objects\port.d)
I (..\FreeRTOS\include\FreeRTOS.h)(0x61C570D0)
I (E:\MDK533\ARM\ARMCC\include\stddef.h)(0x5E8E2EB2)
I (E:\MDK533\ARM\ARMCC\include\stdint.h)(0x5E8E2EB2)
I (..\FreeRTOS\FreeRTOSConfig.h)(0x63ABA262)
I (..\CMSIS\gd32f4xx.h)(0x660A7014)
I (..\CMSIS\core_cm4.h)(0x65A7AAB6)
I (..\CMSIS\core_cmInstr.h)(0x65A7AAB6)
I (..\CMSIS\core_cmFunc.h)(0x65A7AAB6)
I (..\CMSIS\core_cm4_simd.h)(0x65A7AAB6)
I (..\CMSIS\system_gd32f4xx.h)(0x65A7AAB6)
I (..\User\gd32f4xx_libopt.h)(0x65A7AAAA)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6382D7BE)
I (E:\MDK533\ARM\ARMCC\include\stdlib.h)(0x5E8E2EB2)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735A)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735A)
I (..\Protocol\USART0\USART0.h)(0x66605274)
I (..\HeaderFiles\HeaderFiles.h)(0x68941062)
I (..\User\systick.h)(0x66050E9C)
I (E:\MDK533\ARM\ARMCC\include\stdio.h)(0x5E8E2EB2)
I (..\Implement\Implement.h)(0x66605274)
I (..\HardWare\LED\LED.h)(0x664425B2)
I (..\HardWare\KEY\KEY.h)(0x66605158)
I (..\System\TIMER\TIMER.h)(0x66605274)
I (..\LWIP\lwip_app\lwip_comm\lwip_comm.h)(0x66605274)
I (..\HardWare\LAN8720\lan8720.h)(0x6660512C)
I (..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h)(0x66605274)
I (..\LWIP\lwip-1.4.1\src\include\lwip/err.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/opt.h)(0x50CEFB60)
I (..\LWIP\lwip_app\lwip_comm\lwipopts.h)(0x636DA1B0)
I (..\LWIP\lwip-1.4.1\src\include\lwip/debug.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/arch.h)(0x50CEF618)
I (..\LWIP\arch/cc.h)(0x5444DAAA)
I (..\LWIP\arch/cpu.h)(0x5444DC5A)
I (..\LWIP\lwip-1.4.1\src\include\lwip/netif.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h)(0x50CEFB60)
I (..\LWIP\lwip-1.4.1\src\include\lwip/def.h)(0x50CEF618)
I (..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h)(0x54462AD2)
I (..\User\bsp.h)(0x636CB7DE)
I (..\HardWare\SRAM\SRAM.h)(0x66605274)
I (..\MALLOC\malloc.h)(0x63E87362)
I (..\FreeRTOS\include\projdefs.h)(0x61C570D0)
I (..\FreeRTOS\include\portable.h)(0x61C570D0)
I (..\FreeRTOS\include\deprecated_definitions.h)(0x61C570D0)
I (..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h)(0x61C570D0)
I (..\FreeRTOS\include\mpu_wrappers.h)(0x61C570D0)
I (..\FreeRTOS\include\task.h)(0x61C570D0)
I (..\FreeRTOS\include\list.h)(0x61C570D0)
F (..\Readme\Readme.txt)(0x66605274)()
