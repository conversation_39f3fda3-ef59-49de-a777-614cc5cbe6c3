.\objects\event_groups.o: ..\FreeRTOS\src\event_groups.c
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\event_groups.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\event_groups.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\event_groups.o: ..\CMSIS\gd32f4xx.h
.\objects\event_groups.o: ..\CMSIS\core_cm4.h
.\objects\event_groups.o: ..\CMSIS\core_cmInstr.h
.\objects\event_groups.o: ..\CMSIS\core_cmFunc.h
.\objects\event_groups.o: ..\CMSIS\core_cm4_simd.h
.\objects\event_groups.o: ..\CMSIS\system_gd32f4xx.h
.\objects\event_groups.o: ..\User\gd32f4xx_libopt.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\event_groups.o: ..\CMSIS\gd32f4xx.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\event_groups.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\event_groups.o: ..\Protocol\USART0\USART0.h
.\objects\event_groups.o: ..\HeaderFiles\HeaderFiles.h
.\objects\event_groups.o: ..\User\systick.h
.\objects\event_groups.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\event_groups.o: ..\Implement\Implement.h
.\objects\event_groups.o: ..\HeaderFiles\HeaderFiles.h
.\objects\event_groups.o: ..\HardWare\LED\LED.h
.\objects\event_groups.o: ..\HardWare\KEY\KEY.h
.\objects\event_groups.o: ..\System\TIMER\TIMER.h
.\objects\event_groups.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\event_groups.o: ..\HardWare\LAN8720\lan8720.h
.\objects\event_groups.o: ..\Protocol\USART0\USART0.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\event_groups.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\event_groups.o: ..\LWIP\arch/cc.h
.\objects\event_groups.o: ..\LWIP\arch/cpu.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\event_groups.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\event_groups.o: ..\User\bsp.h
.\objects\event_groups.o: ..\HardWare\SRAM\SRAM.h
.\objects\event_groups.o: ..\MALLOC\malloc.h
.\objects\event_groups.o: ..\HardWare\LCD\LCD.h
.\objects\event_groups.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\event_groups.o: ..\FreeRTOS\include\projdefs.h
.\objects\event_groups.o: ..\FreeRTOS\include\portable.h
.\objects\event_groups.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\event_groups.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\event_groups.o: ..\FreeRTOS\include\mpu_wrappers.h
.\objects\event_groups.o: ..\FreeRTOS\include\task.h
.\objects\event_groups.o: ..\FreeRTOS\include\list.h
.\objects\event_groups.o: ..\FreeRTOS\include\timers.h
.\objects\event_groups.o: ..\FreeRTOS\include\event_groups.h
