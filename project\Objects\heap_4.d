.\objects\heap_4.o: ..\FreeRTOS\port\MemMang\heap_4.c
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\heap_4.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\heap_4.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\heap_4.o: ..\CMSIS\gd32f4xx.h
.\objects\heap_4.o: ..\CMSIS\core_cm4.h
.\objects\heap_4.o: ..\CMSIS\core_cmInstr.h
.\objects\heap_4.o: ..\CMSIS\core_cmFunc.h
.\objects\heap_4.o: ..\CMSIS\core_cm4_simd.h
.\objects\heap_4.o: ..\CMSIS\system_gd32f4xx.h
.\objects\heap_4.o: ..\User\gd32f4xx_libopt.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\heap_4.o: ..\CMSIS\gd32f4xx.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\heap_4.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\heap_4.o: ..\Protocol\USART0\USART0.h
.\objects\heap_4.o: ..\HeaderFiles\HeaderFiles.h
.\objects\heap_4.o: ..\User\systick.h
.\objects\heap_4.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\heap_4.o: ..\Implement\Implement.h
.\objects\heap_4.o: ..\HeaderFiles\HeaderFiles.h
.\objects\heap_4.o: ..\HardWare\LED\LED.h
.\objects\heap_4.o: ..\HardWare\KEY\KEY.h
.\objects\heap_4.o: ..\System\TIMER\TIMER.h
.\objects\heap_4.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\heap_4.o: ..\HardWare\LAN8720\lan8720.h
.\objects\heap_4.o: ..\Protocol\USART0\USART0.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\heap_4.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\heap_4.o: ..\LWIP\arch/cc.h
.\objects\heap_4.o: ..\LWIP\arch/cpu.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\heap_4.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\heap_4.o: ..\User\bsp.h
.\objects\heap_4.o: ..\HardWare\SRAM\SRAM.h
.\objects\heap_4.o: ..\MALLOC\malloc.h
.\objects\heap_4.o: ..\HardWare\LCD\LCD.h
.\objects\heap_4.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\heap_4.o: ..\FreeRTOS\include\projdefs.h
.\objects\heap_4.o: ..\FreeRTOS\include\portable.h
.\objects\heap_4.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\heap_4.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\heap_4.o: ..\FreeRTOS\include\mpu_wrappers.h
.\objects\heap_4.o: ..\FreeRTOS\include\task.h
.\objects\heap_4.o: ..\FreeRTOS\include\list.h
