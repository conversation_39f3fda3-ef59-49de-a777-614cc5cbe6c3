.\objects\if_api.o: ..\LWIP\src\api\if_api.c
.\objects\if_api.o: ..\LWIP\src\include\lwip/opt.h
.\objects\if_api.o: ..\LWIP\arch\lwipopts.h
.\objects\if_api.o: ..\LWIP\src\include\lwip/debug.h
.\objects\if_api.o: ..\LWIP\src\include\lwip/arch.h
.\objects\if_api.o: ..\LWIP\arch/cc.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\inttypes.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
.\objects\if_api.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\ctype.h
.\objects\if_api.o: ..\LWIP\src\include\lwip/opt.h
