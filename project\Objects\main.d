.\objects\main.o: ..\User\main.c
.\objects\main.o: ..\HeaderFiles\HeaderFiles.h
.\objects\main.o: ..\CMSIS\gd32f4xx.h
.\objects\main.o: ..\CMSIS\core_cm4.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\main.o: ..\CMSIS\core_cmInstr.h
.\objects\main.o: ..\CMSIS\core_cmFunc.h
.\objects\main.o: ..\CMSIS\core_cm4_simd.h
.\objects\main.o: ..\CMSIS\system_gd32f4xx.h
.\objects\main.o: ..\User\gd32f4xx_libopt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\main.o: ..\CMSIS\gd32f4xx.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\main.o: ..\User\systick.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\main.o: ..\Implement\Implement.h
.\objects\main.o: ..\HeaderFiles\HeaderFiles.h
.\objects\main.o: ..\HardWare\LED\LED.h
.\objects\main.o: ..\HardWare\KEY\KEY.h
.\objects\main.o: ..\System\TIMER\TIMER.h
.\objects\main.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\main.o: ..\HardWare\LAN8720\lan8720.h
.\objects\main.o: ..\Protocol\USART0\USART0.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\main.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\main.o: ..\LWIP\arch/cc.h
.\objects\main.o: ..\LWIP\arch/cpu.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\main.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\main.o: ..\User\bsp.h
.\objects\main.o: ..\HardWare\SRAM\SRAM.h
.\objects\main.o: ..\MALLOC\malloc.h
.\objects\main.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\main.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\main.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\main.o: ..\FreeRTOS\include\projdefs.h
.\objects\main.o: ..\FreeRTOS\include\portable.h
.\objects\main.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\main.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\main.o: ..\FreeRTOS\include\mpu_wrappers.h
