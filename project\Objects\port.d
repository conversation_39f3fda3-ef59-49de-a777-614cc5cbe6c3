.\objects\port.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\port.c
.\objects\port.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\port.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\port.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\port.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\port.o: ..\CMSIS\gd32f4xx.h
.\objects\port.o: ..\CMSIS\core_cm4.h
.\objects\port.o: ..\CMSIS\core_cmInstr.h
.\objects\port.o: ..\CMSIS\core_cmFunc.h
.\objects\port.o: ..\CMSIS\core_cm4_simd.h
.\objects\port.o: ..\CMSIS\system_gd32f4xx.h
.\objects\port.o: ..\User\gd32f4xx_libopt.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\port.o: ..\CMSIS\gd32f4xx.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\port.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\port.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\port.o: ..\Protocol\USART0\USART0.h
.\objects\port.o: ..\HeaderFiles\HeaderFiles.h
.\objects\port.o: ..\User\systick.h
.\objects\port.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\port.o: ..\Implement\Implement.h
.\objects\port.o: ..\HeaderFiles\HeaderFiles.h
.\objects\port.o: ..\HardWare\LED\LED.h
.\objects\port.o: ..\HardWare\KEY\KEY.h
.\objects\port.o: ..\System\TIMER\TIMER.h
.\objects\port.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\port.o: ..\HardWare\LAN8720\lan8720.h
.\objects\port.o: ..\Protocol\USART0\USART0.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\port.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\port.o: ..\LWIP\arch/cc.h
.\objects\port.o: ..\LWIP\arch/cpu.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\port.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\port.o: ..\User\bsp.h
.\objects\port.o: ..\HardWare\SRAM\SRAM.h
.\objects\port.o: ..\MALLOC\malloc.h
.\objects\port.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\port.o: ..\FreeRTOS\include\projdefs.h
.\objects\port.o: ..\FreeRTOS\include\portable.h
.\objects\port.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\port.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\port.o: ..\FreeRTOS\include\mpu_wrappers.h
.\objects\port.o: ..\FreeRTOS\include\task.h
.\objects\port.o: ..\FreeRTOS\include\list.h
