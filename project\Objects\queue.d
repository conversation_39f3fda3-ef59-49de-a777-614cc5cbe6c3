.\objects\queue.o: ..\FreeRTOS\src\queue.c
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\queue.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\queue.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\queue.o: ..\CMSIS\gd32f4xx.h
.\objects\queue.o: ..\CMSIS\core_cm4.h
.\objects\queue.o: ..\CMSIS\core_cmInstr.h
.\objects\queue.o: ..\CMSIS\core_cmFunc.h
.\objects\queue.o: ..\CMSIS\core_cm4_simd.h
.\objects\queue.o: ..\CMSIS\system_gd32f4xx.h
.\objects\queue.o: ..\User\gd32f4xx_libopt.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\queue.o: ..\CMSIS\gd32f4xx.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\queue.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\queue.o: ..\Protocol\USART0\USART0.h
.\objects\queue.o: ..\HeaderFiles\HeaderFiles.h
.\objects\queue.o: ..\User\systick.h
.\objects\queue.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\queue.o: ..\Implement\Implement.h
.\objects\queue.o: ..\HeaderFiles\HeaderFiles.h
.\objects\queue.o: ..\HardWare\LED\LED.h
.\objects\queue.o: ..\HardWare\KEY\KEY.h
.\objects\queue.o: ..\System\TIMER\TIMER.h
.\objects\queue.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\queue.o: ..\HardWare\LAN8720\lan8720.h
.\objects\queue.o: ..\Protocol\USART0\USART0.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\queue.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\queue.o: ..\LWIP\arch/cc.h
.\objects\queue.o: ..\LWIP\arch/cpu.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\queue.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\queue.o: ..\User\bsp.h
.\objects\queue.o: ..\HardWare\SRAM\SRAM.h
.\objects\queue.o: ..\MALLOC\malloc.h
.\objects\queue.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\queue.o: ..\FreeRTOS\include\projdefs.h
.\objects\queue.o: ..\FreeRTOS\include\portable.h
.\objects\queue.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\queue.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\queue.o: ..\FreeRTOS\include\mpu_wrappers.h
.\objects\queue.o: ..\FreeRTOS\include\task.h
.\objects\queue.o: ..\FreeRTOS\include\list.h
.\objects\queue.o: ..\FreeRTOS\include\queue.h
