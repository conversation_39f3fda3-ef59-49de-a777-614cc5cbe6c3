.\objects\tasks.o: ..\FreeRTOS\src\tasks.c
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\string.h
.\objects\tasks.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\tasks.o: ..\FreeRTOS\FreeRTOSConfig.h
.\objects\tasks.o: ..\CMSIS\gd32f4xx.h
.\objects\tasks.o: ..\CMSIS\core_cm4.h
.\objects\tasks.o: ..\CMSIS\core_cmInstr.h
.\objects\tasks.o: ..\CMSIS\core_cmFunc.h
.\objects\tasks.o: ..\CMSIS\core_cm4_simd.h
.\objects\tasks.o: ..\CMSIS\system_gd32f4xx.h
.\objects\tasks.o: ..\User\gd32f4xx_libopt.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\tasks.o: ..\CMSIS\gd32f4xx.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\tasks.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\tasks.o: ..\Protocol\USART0\USART0.h
.\objects\tasks.o: ..\HeaderFiles\HeaderFiles.h
.\objects\tasks.o: ..\User\systick.h
.\objects\tasks.o: E:\MDK533\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\tasks.o: ..\Implement\Implement.h
.\objects\tasks.o: ..\HeaderFiles\HeaderFiles.h
.\objects\tasks.o: ..\HardWare\LED\LED.h
.\objects\tasks.o: ..\HardWare\KEY\KEY.h
.\objects\tasks.o: ..\System\TIMER\TIMER.h
.\objects\tasks.o: ..\LWIP\lwip_app\lwip_comm\lwip_comm.h
.\objects\tasks.o: ..\HardWare\LAN8720\lan8720.h
.\objects\tasks.o: ..\Protocol\USART0\USART0.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\netif\ethernetif.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/err.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\tasks.o: ..\LWIP\lwip_app\lwip_comm\lwipopts.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/debug.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/arch.h
.\objects\tasks.o: ..\LWIP\arch/cc.h
.\objects\tasks.o: ..\LWIP\arch/cpu.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/opt.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/netif.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\ipv4\lwip/ip_addr.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/def.h
.\objects\tasks.o: ..\LWIP\lwip-1.4.1\src\include\lwip/pbuf.h
.\objects\tasks.o: ..\User\bsp.h
.\objects\tasks.o: ..\HardWare\SRAM\SRAM.h
.\objects\tasks.o: ..\MALLOC\malloc.h
.\objects\tasks.o: ..\HardWare\LCD\LCD.h
.\objects\tasks.o: ..\FreeRTOS\include\FreeRTOS.h
.\objects\tasks.o: ..\FreeRTOS\include\projdefs.h
.\objects\tasks.o: ..\FreeRTOS\include\portable.h
.\objects\tasks.o: ..\FreeRTOS\include\deprecated_definitions.h
.\objects\tasks.o: ..\FreeRTOS\port\RVDS\ARM_CM4F\portmacro.h
.\objects\tasks.o: ..\FreeRTOS\include\mpu_wrappers.h
.\objects\tasks.o: ..\FreeRTOS\include\task.h
.\objects\tasks.o: ..\FreeRTOS\include\list.h
.\objects\tasks.o: ..\FreeRTOS\include\timers.h
.\objects\tasks.o: ..\FreeRTOS\include\StackMacros.h
